"""
MODULE JULIA AUTONOME - AnalyseurVraisValeurs
============================================

ANALYSEUR DES VRAIES VALEURS OBSERVÉES
Calcule les 8 métriques d'entropie pour chaque valeur INDEX5 réellement observée dans une partie.
Contrairement au système de prédiction qui simule 6 possibilités, ce module analyse les vraies valeurs.

FONCTIONNALITÉS :
- Charge les données depuis dataset.json avec JSON3
- Utilise Analyseur.jl pour les calculs de métriques
- Calcule les 8 métriques + différentiels pour chaque main observée
- Exporte les résultats au format TXT

USAGE :
    using AnalyseurVraisValeurs
    analyser_partie_vraies_valeurs(numero_partie)

DÉPENDANCES : JSON3, Analyseur.jl (PredicteurIndex5)
"""

module AnalyseurVraisValeurs

# ═══════════════════════════════════════════════════════════════════════════════
# IMPORTS ET DÉPENDANCES
# ═══════════════════════════════════════════════════════════════════════════════

using JSON3
using Printf
using Dates

# Import du module Analyseur.jl (PredicteurIndex5) pour utiliser ses fonctions
include("Analyseur.jl")
using .PredicteurIndex5

# ═══════════════════════════════════════════════════════════════════════════════
# EXPORTS PUBLICS
# ═══════════════════════════════════════════════════════════════════════════════

export analyser_partie_vraies_valeurs, charger_donnees_dataset, main_analyseur

# ═══════════════════════════════════════════════════════════════════════════════
# CONSTANTES
# ═══════════════════════════════════════════════════════════════════════════════

const CHEMIN_DATASET = "C:\\Users\\<USER>\\Desktop\\24\\modules\\partie\\dataset.json"

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTIONS DE CHARGEMENT DE DONNÉES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    charger_donnees_dataset(chemin_fichier::String) -> Dict

Charge les données du fichier dataset.json en utilisant JSON3.
Retourne la structure complète des données.

# Arguments
- `chemin_fichier::String`: Chemin vers le fichier dataset.json

# Returns
- `Dict`: Structure des données chargées depuis JSON

# Throws
- `SystemError`: Si le fichier ne peut pas être lu
- `JSON3.Error`: Si le JSON est malformé

# Exemples
```julia
donnees = charger_donnees_dataset("partie/dataset.json")
```
"""
function charger_donnees_dataset(chemin_fichier::String)
    println("📖 Chargement du dataset : $chemin_fichier")
    
    try
        # Lire le fichier en tant que chaîne
        contenu_json = read(chemin_fichier, String)
        
        # Parser avec JSON3
        donnees = JSON3.read(contenu_json)
        
        println("✅ Dataset chargé avec succès")
        return donnees
        
    catch e
        println("❌ Erreur lors du chargement du dataset : $e")
        rethrow(e)
    end
end

"""
    extraire_partie_dataset(donnees, numero_partie::Int) -> Vector{MainData}

Extrait une partie spécifique du dataset et la convertit en format MainData
compatible avec Analyseur.jl.

# Arguments
- `donnees`: Données chargées depuis dataset.json
- `numero_partie::Int`: Numéro de la partie à extraire

# Returns
- `Vector{MainData}`: Vecteur des mains de la partie au format MainData

# Throws
- `ArgumentError`: Si la partie n'existe pas ou est invalide
"""
function extraire_partie_dataset(donnees, numero_partie::Int)
    println("🎯 Extraction de la partie numéro : $numero_partie")
    
    # Vérifier que les données contiennent des parties
    if !haskey(donnees, "parties_condensees") || isempty(donnees["parties_condensees"])
        throw(ArgumentError("Format de dataset invalide : 'parties_condensees' manquant ou vide"))
    end
    
    # Chercher la partie demandée
    partie_trouvee = nothing
    for partie in donnees["parties_condensees"]
        if partie["partie_number"] == numero_partie
            partie_trouvee = partie
            break
        end
    end
    
    if partie_trouvee === nothing
        throw(ArgumentError("Partie numéro $numero_partie non trouvée dans le dataset"))
    end
    
    # Convertir les mains en format MainData
    mains_data = partie_trouvee["mains_condensees"]
    mains = Vector{MainData}()
    sizehint!(mains, length(mains_data))
    
    for main_data in mains_data
        # Ignorer les mains avec des données manquantes
        if main_data["main_number"] === nothing ||
           main_data["index1"] === "" ||
           main_data["index2"] === "" ||
           main_data["index3"] === ""
            continue
        end
        
        push!(mains, MainData(
            main_data["main_number"],
            main_data["manche_pb_number"],
            main_data["index1"],
            main_data["index2"],
            main_data["index3"],
            main_data["index5"]
        ))
    end
    
    println("✅ Partie $numero_partie extraite : $(length(mains)) mains valides")
    return mains
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTION PRINCIPALE D'ANALYSE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    analyser_partie_vraies_valeurs(numero_partie::Int) -> String

Analyse une partie complète en calculant les 8 métriques d'entropie pour chaque
valeur INDEX5 réellement observée. Contrairement au système de prédiction qui
simule 6 possibilités, cette fonction analyse les vraies valeurs.

# Arguments
- `numero_partie::Int`: Numéro de la partie à analyser

# Returns
- `String`: Nom du fichier TXT généré avec les résultats

# Processus
1. Charge les données depuis dataset.json
2. Extrait la partie demandée
3. Pour chaque main (à partir de la main 3) :
   - Calcule les 8 métriques pour la séquence jusqu'à cette main
   - Calcule les différentiels par rapport à la main précédente
   - Enregistre les vraies valeurs observées (pas de simulation)
4. Exporte tout au format TXT

# Exemples
```julia
fichier_resultat = analyser_partie_vraies_valeurs(1)
```
"""
function analyser_partie_vraies_valeurs(numero_partie::Int)
    println("🚀 ANALYSEUR DES VRAIES VALEURS - PARTIE $numero_partie")
    println("="^80)
    
    try
        # 1. Charger les données du dataset
        donnees = charger_donnees_dataset(CHEMIN_DATASET)
        
        # 2. Extraire la partie demandée
        mains = extraire_partie_dataset(donnees, numero_partie)
        
        if isempty(mains)
            throw(ArgumentError("Aucune main valide trouvée dans la partie $numero_partie"))
        end
        
        # 3. Préparer les structures pour les calculs
        formulas = FormulasTheoretical{Float64}()
        sequence_index5 = [main.index5 for main in mains]
        
        # 4. Calculer les métriques pour chaque main (à partir de la main 3)
        max_mains = min(59, length(mains))
        println("📊 Analyse de $max_mains mains (à partir de la main 3)...")
        println("ℹ️  Les mains 1 et 2 sont ignorées car BlockT et TauxT nécessitent au moins 3 mains")
        
        # Structure pour stocker les résultats
        resultats_analyse = []
        
        # Métriques de la main précédente pour calculer les différentiels
        metriques_precedente = nothing
        
        for i in 3:max_mains
            main_actuelle = mains[i]
            sequence_jusqu_n = sequence_index5[1:i]
            
            # Calculer toutes les métriques pour cette main
            metriques_actuelles = calculer_toutes_metriques_theoriques(formulas, sequence_jusqu_n, i)
            
            # Calculer les différentiels si on a une main précédente
            differentiels = nothing
            if metriques_precedente !== nothing
                calculateur_diff = CalculateurDifferentielsPredictifs{Float64}()
                differentiels = calculer_differentiels_predictifs(calculateur_diff, metriques_precedente, metriques_actuelles)
            end
            
            # Stocker les résultats
            push!(resultats_analyse, (
                main_number = main_actuelle.main_number,
                index5_observe = main_actuelle.index5,
                metriques = metriques_actuelles,
                differentiels = differentiels
            ))
            
            # Sauvegarder pour la prochaine itération
            metriques_precedente = metriques_actuelles
        end
        
        # 5. Exporter les résultats vers un fichier TXT
        nom_fichier = "analyse_vraies_valeurs_partie_$(numero_partie)_$(Dates.format(now(), "yyyymmdd_HHMMSS")).txt"
        exporter_resultats_txt(nom_fichier, numero_partie, resultats_analyse, max_mains)
        
        println("\n✅ ANALYSE TERMINÉE")
        println("📁 Résultats exportés vers : $nom_fichier")
        println("📊 $(length(resultats_analyse)) mains analysées")
        
        return nom_fichier
        
    catch e
        println("💥 Erreur lors de l'analyse : $e")
        rethrow(e)
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTIONS D'EXPORT
# ═══════════════════════════════════════════════════════════════════════════════

"""
    exporter_resultats_txt(nom_fichier::String, numero_partie::Int, resultats, max_mains::Int)

Exporte les résultats d'analyse au format TXT, similaire au rapport de prédiction
mais avec les vraies valeurs observées au lieu des simulations.

# Arguments
- `nom_fichier::String`: Nom du fichier de sortie
- `numero_partie::Int`: Numéro de la partie analysée
- `resultats`: Vecteur des résultats d'analyse
- `max_mains::Int`: Nombre maximum de mains analysées
"""
function exporter_resultats_txt(nom_fichier::String, numero_partie::Int, resultats, max_mains::Int)
    println("📝 Export des résultats vers : $nom_fichier")

    open(nom_fichier, "w") do io
        # En-tête du rapport
        write(io, "ANALYSE DES VRAIES VALEURS OBSERVÉES\n")
        write(io, "="^80, "\n")
        write(io, "Partie analysée : $numero_partie\n")
        write(io, "Date d'analyse : $(Dates.format(now(), "dd/mm/yyyy à HH:MM:SS"))\n")
        write(io, "Nombre de mains analysées : $(length(resultats))\n")
        write(io, "Plage d'analyse : Main 3 à Main $max_mains\n")
        write(io, "\n")

        write(io, "DESCRIPTION :\n")
        write(io, "Ce rapport présente les 8 métriques d'entropie calculées pour chaque\n")
        write(io, "valeur INDEX5 réellement observée dans la partie, ainsi que les\n")
        write(io, "différentiels par rapport à la main précédente.\n")
        write(io, "\n")

        write(io, "MÉTRIQUES ANALYSÉES :\n")
        write(io, "1. CondT    - Entropie conditionnelle moyenne (PRIORITÉ 1)\n")
        write(io, "2. DivKLT   - Divergence Kullback-Leibler (PRIORITÉ 2)\n")
        write(io, "3. CrossT   - Entropie croisée (PRIORITÉ 2)\n")
        write(io, "4. MetricT  - Entropie métrique pondérée (PRIORITÉ 3)\n")
        write(io, "5. TopoT    - Entropie topologique multi-échelles (PRIORITÉ 3)\n")
        write(io, "6. TauxT    - Taux d'entropie (PRIORITÉ 4)\n")
        write(io, "7. ShannonT - Entropie de Shannon théorique (PRIORITÉ 5)\n")
        write(io, "8. BlockT   - Entropie de bloc cumulative (PRIORITÉ 5)\n")
        write(io, "\n")

        # En-tête du tableau
        write(io, "="^120, "\n")
        write(io, "RÉSULTATS DÉTAILLÉS\n")
        write(io, "="^120, "\n")
        write(io, "\n")

        # En-tête des colonnes
        write(io, @sprintf("%-6s %-15s %-12s %-12s %-12s %-12s %-12s %-12s %-12s %-12s\n",
            "MAIN", "INDEX5", "CondT|Diff", "DivKLT|Diff", "CrossT|Diff", "MetricT|Diff",
            "TopoT|Diff", "TauxT|Diff", "ShannonT|Diff", "BlockT|Diff"))
        write(io, "-"^120, "\n")

        # Données pour chaque main
        for resultat in resultats
            main_num = resultat.main_number
            index5 = resultat.index5_observe
            metriques = resultat.metriques
            diff = resultat.differentiels

            # Formater les métriques avec différentiels
            if diff !== nothing
                cond_str = @sprintf("%-6.4f|%-5.4f", metriques.cond_t, diff.diff_cond_t)
                divkl_str = @sprintf("%-6.4f|%-5.4f", metriques.div_kl_t, diff.diff_div_kl_t)
                cross_str = @sprintf("%-6.4f|%-5.4f", metriques.cross_t, diff.diff_cross_t)
                metric_str = @sprintf("%-7.4f|%-4.4f", metriques.metric_t, diff.diff_metric_t)
                topo_str = @sprintf("%-6.4f|%-5.4f", metriques.topo_t, diff.diff_topo_t)
                taux_str = @sprintf("%-6.4f|%-5.4f", metriques.taux_t, diff.diff_taux_t)
                shannon_str = @sprintf("%-7.4f|%-4.4f", metriques.shannon_t, diff.diff_shannon_t)
                block_str = @sprintf("%-7.4f|%-4.4f", metriques.block_t, diff.diff_block_t)
            else
                # Première main (pas de différentiel)
                cond_str = @sprintf("%-6.4f|%-5s", metriques.cond_t, "N/A")
                divkl_str = @sprintf("%-6.4f|%-5s", metriques.div_kl_t, "N/A")
                cross_str = @sprintf("%-6.4f|%-5s", metriques.cross_t, "N/A")
                metric_str = @sprintf("%-7.4f|%-4s", metriques.metric_t, "N/A")
                topo_str = @sprintf("%-6.4f|%-5s", metriques.topo_t, "N/A")
                taux_str = @sprintf("%-6.4f|%-5s", metriques.taux_t, "N/A")
                shannon_str = @sprintf("%-7.4f|%-4s", metriques.shannon_t, "N/A")
                block_str = @sprintf("%-7.4f|%-4s", metriques.block_t, "N/A")
            end

            write(io, @sprintf("%-6d %-15s %-12s %-12s %-12s %-12s %-12s %-12s %-12s %-12s\n",
                main_num, index5, cond_str, divkl_str, cross_str, metric_str,
                topo_str, taux_str, shannon_str, block_str))
        end

        # Pied de page
        write(io, "\n")
        write(io, "="^120, "\n")
        write(io, "NOTES :\n")
        write(io, "- Les valeurs sont les métriques calculées pour la séquence [main 1:main n]\n")
        write(io, "- Les différentiels (Diff) montrent l'évolution par rapport à la main précédente\n")
        write(io, "- N/A indique qu'aucun différentiel n'est disponible (première main analysée)\n")
        write(io, "- Toutes les métriques utilisent les probabilités théoriques INDEX5\n")
        write(io, "\n")
        write(io, "Généré par AnalyseurVraisValeurs.jl - $(Dates.format(now(), "dd/mm/yyyy HH:MM:SS"))\n")
    end

    println("✅ Export terminé")
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTION MAIN POUR EXÉCUTION DIRECTE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    main_analyseur()

Fonction principale pour exécution directe du module.
Demande à l'utilisateur quelle partie analyser et lance l'analyse.
"""
function main_analyseur()
    println("🎯 ANALYSEUR DES VRAIES VALEURS OBSERVÉES")
    println("="^50)

    try
        print("Entrez le numéro de la partie à analyser : ")
        numero_partie = parse(Int, readline())

        fichier_resultat = analyser_partie_vraies_valeurs(numero_partie)

        println("\n🎉 Analyse terminée avec succès !")
        println("📁 Fichier généré : $fichier_resultat")

    catch e
        if isa(e, ArgumentError)
            println("❌ Erreur : $(e.msg)")
        else
            println("💥 Erreur inattendue : $e")
        end
    end
end

# Exécution automatique si le script est lancé directement
if abspath(PROGRAM_FILE) == @__FILE__
    main_analyseur()
end

end # module AnalyseurVraisValeurs
