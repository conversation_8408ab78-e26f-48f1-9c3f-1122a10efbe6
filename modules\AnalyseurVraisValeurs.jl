"""
    AnalyseurVraisValeurs

A professional Julia module for analyzing real observed INDEX5 values in baccarat games.

This module calculates the 8 entropy metrics for each INDEX5 value that actually occurred
in a game, as opposed to the prediction system which simulates 6 possibilities.

# Main Types
- [`RealValueAnalyzer`](@ref): Main analyzer for real values
- [`AnalysisResult`](@ref): Container for analysis results
- [`AnalysisConfig`](@ref): Configuration for analysis parameters

# Main Functions
- [`analyze_real_values`](@ref): Analyze a complete game with real values
- [`load_dataset`](@ref): Load data from dataset.json
- [`export_results`](@ref): Export results to TXT format

# Examples
```julia
using AnalyseurVraisValeurs

# Analyze game number 1
analyzer = RealValueAnalyzer()
results = analyze_real_values(analyzer, 1)
export_results(results, "analysis_game_1.txt")
```

# Dependencies
- JSON3: For loading dataset.json files
- PredicteurIndex5: For entropy calculations (Analyseur.jl)

See the documentation for detailed usage examples and API reference.
"""
module AnalyseurVraisValeurs

# ═══════════════════════════════════════════════════════════════════════════════
# IMPORTS AND DEPENDENCIES
# ═══════════════════════════════════════════════════════════════════════════════

# Standard library
using Dates
using Printf

# External dependencies
using JSON3

# Internal dependencies - Import PredicteurIndex5 module
include("Analyseur.jl")
using .PredicteurIndex5

# ═══════════════════════════════════════════════════════════════════════════════
# EXPORTS
# ═══════════════════════════════════════════════════════════════════════════════

# Main types
export RealValueAnalyzer, AnalysisResult, AnalysisConfig

# Main functions
export analyze_real_values, load_dataset, export_results

# Utility functions
export create_analyzer, validate_game_data

# ═══════════════════════════════════════════════════════════════════════════════
# CONSTANTS
# ═══════════════════════════════════════════════════════════════════════════════

const DEFAULT_DATASET_PATH = "C:\\Users\\<USER>\\Desktop\\24\\modules\\partie\\dataset.json"
const MIN_HANDS_FOR_ANALYSIS = 3
const MAX_HANDS_PER_GAME = 59

# ═══════════════════════════════════════════════════════════════════════════════
# CUSTOM EXCEPTION TYPES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    DatasetError <: Exception

Exception thrown when there are issues with dataset loading or parsing.
"""
struct DatasetError <: Exception
    message::String
end

"""
    GameNotFoundError <: Exception

Exception thrown when a requested game number is not found in the dataset.
"""
struct GameNotFoundError <: Exception
    game_number::Int
    message::String
end

GameNotFoundError(game_number::Int) = GameNotFoundError(game_number, "Game number $game_number not found in dataset")

"""
    InvalidGameDataError <: Exception

Exception thrown when game data is invalid or corrupted.
"""
struct InvalidGameDataError <: Exception
    game_number::Int
    message::String
end

# ═══════════════════════════════════════════════════════════════════════════════
# TYPE DEFINITIONS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    AnalysisConfig{T<:AbstractFloat}

Configuration parameters for real value analysis.

# Fields
- `max_hands::Int`: Maximum number of hands to analyze (default: 59)
- `min_hands::Int`: Minimum number of hands required (default: 3)
- `precision::Type{T}`: Floating point precision for calculations (default: Float64)
- `export_format::Symbol`: Export format (:txt, :csv, :json) (default: :txt)

# Examples
```julia
config = AnalysisConfig{Float64}()
config_custom = AnalysisConfig{Float32}(max_hands=30, precision=Float32)
```
"""
struct AnalysisConfig{T<:AbstractFloat}
    max_hands::Int
    min_hands::Int
    precision::Type{T}
    export_format::Symbol

    function AnalysisConfig{T}(
        max_hands::Int = MAX_HANDS_PER_GAME,
        min_hands::Int = MIN_HANDS_FOR_ANALYSIS,
        precision::Type{T} = T,
        export_format::Symbol = :txt
    ) where T<:AbstractFloat
        if max_hands < min_hands
            throw(ArgumentError("max_hands ($max_hands) must be >= min_hands ($min_hands)"))
        end
        if min_hands < 1
            throw(ArgumentError("min_hands must be >= 1"))
        end
        if !(export_format in (:txt, :csv, :json))
            throw(ArgumentError("export_format must be one of :txt, :csv, :json"))
        end
        new{T}(max_hands, min_hands, precision, export_format)
    end
end

# Convenience constructor
AnalysisConfig(args...) = AnalysisConfig{Float64}(args...)

"""
    RealValueAnalyzer{T<:AbstractFloat}

Main analyzer for processing real observed INDEX5 values.

# Fields
- `config::AnalysisConfig{T}`: Analysis configuration
- `formulas::FormulasTheoretical{T}`: Theoretical formulas for entropy calculations

# Examples
```julia
analyzer = RealValueAnalyzer()
analyzer_f32 = RealValueAnalyzer{Float32}()
```
"""
struct RealValueAnalyzer{T<:AbstractFloat}
    config::AnalysisConfig{T}
    formulas::FormulasTheoretical{T}

    function RealValueAnalyzer{T}(config::AnalysisConfig{T} = AnalysisConfig{T}()) where T<:AbstractFloat
        formulas = FormulasTheoretical{T}()
        new{T}(config, formulas)
    end
end

# Convenience constructor
RealValueAnalyzer(config::AnalysisConfig{T} = AnalysisConfig()) where T = RealValueAnalyzer{T}(config)

"""
    HandAnalysis{T<:AbstractFloat}

Analysis results for a single hand.

# Fields
- `hand_number::Int`: Hand number in the game
- `index5_observed::String`: The actual INDEX5 value observed
- `metrics::MetriquesTheorique{T}`: Calculated entropy metrics
- `differentials::Union{DifferentielsPredictifs{T}, Nothing}`: Differentials from previous hand
"""
struct HandAnalysis{T<:AbstractFloat}
    hand_number::Int
    index5_observed::String
    metrics::MetriquesTheorique{T}
    differentials::Union{DifferentielsPredictifs{T}, Nothing}
end

"""
    AnalysisResult{T<:AbstractFloat}

Complete analysis results for a game.

# Fields
- `game_number::Int`: Game number analyzed
- `total_hands::Int`: Total number of hands in the game
- `analyzed_hands::Int`: Number of hands actually analyzed
- `hand_analyses::Vector{HandAnalysis{T}}`: Analysis for each hand
- `analysis_timestamp::DateTime`: When the analysis was performed
- `config::AnalysisConfig{T}`: Configuration used for analysis

# Examples
```julia
result = analyze_real_values(analyzer, 1)
println("Analyzed $(result.analyzed_hands) hands from game $(result.game_number)")
```
"""
struct AnalysisResult{T<:AbstractFloat}
    game_number::Int
    total_hands::Int
    analyzed_hands::Int
    hand_analyses::Vector{HandAnalysis{T}}
    analysis_timestamp::DateTime
    config::AnalysisConfig{T}
end

# ═══════════════════════════════════════════════════════════════════════════════
# DATA LOADING FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    load_dataset(file_path::String = DEFAULT_DATASET_PATH) -> JSON3.Object

Load data from dataset.json file using JSON3.

# Arguments
- `file_path::String`: Path to the dataset.json file (default: DEFAULT_DATASET_PATH)

# Returns
- `JSON3.Object`: Parsed dataset structure

# Throws
- `DatasetError`: If file cannot be read or JSON is malformed
- `SystemError`: If file access fails

# Examples
```julia
data = load_dataset()
data = load_dataset("custom/path/dataset.json")
```
"""
function load_dataset(file_path::String = DEFAULT_DATASET_PATH)
    try
        if !isfile(file_path)
            throw(DatasetError("Dataset file not found: $file_path"))
        end

        json_content = read(file_path, String)

        if isempty(strip(json_content))
            throw(DatasetError("Dataset file is empty: $file_path"))
        end

        data = JSON3.read(json_content)

        # Validate basic structure
        if !haskey(data, "parties_condensees")
            throw(DatasetError("Invalid dataset format: missing 'parties_condensees' key"))
        end

        return data

    catch e
        if isa(e, DatasetError)
            rethrow(e)
        elseif isa(e, JSON3.Error)
            throw(DatasetError("Invalid JSON format in dataset file: $(e.msg)"))
        else
            throw(DatasetError("Failed to load dataset: $e"))
        end
    end
end

"""
    extract_game_data(dataset, game_number::Int) -> Vector{MainData}

Extract a specific game from the dataset and convert it to MainData format
compatible with Analyseur.jl.

# Arguments
- `dataset`: Dataset loaded from JSON
- `game_number::Int`: Game number to extract

# Returns
- `Vector{MainData}`: Vector of hands in MainData format

# Throws
- `GameNotFoundError`: If the game number is not found
- `InvalidGameDataError`: If game data is invalid or corrupted
"""
function extract_game_data(dataset, game_number::Int)
    # Validate dataset structure
    if isempty(dataset["parties_condensees"])
        throw(DatasetError("Dataset contains no games"))
    end

    # Find the requested game
    game_found = nothing
    for game in dataset["parties_condensees"]
        if game["partie_number"] == game_number
            game_found = game
            break
        end
    end

    if game_found === nothing
        throw(GameNotFoundError(game_number))
    end

    # Validate game structure
    if !haskey(game_found, "mains_condensees")
        throw(InvalidGameDataError(game_number, "Game missing 'mains_condensees' field"))
    end

    hands_data = game_found["mains_condensees"]
    if isempty(hands_data)
        throw(InvalidGameDataError(game_number, "Game contains no hands"))
    end

    # Convert hands to MainData format
    hands = Vector{MainData}()
    sizehint!(hands, length(hands_data))

    for (i, hand_data) in enumerate(hands_data)
        # Validate required fields
        required_fields = ["main_number", "index1", "index2", "index3", "index5"]
        for field in required_fields
            if !haskey(hand_data, field) || hand_data[field] === nothing
                @warn "Hand $i missing or null field '$field', skipping"
                continue
            end
        end

        # Skip hands with empty string indices (except index5 which can be empty)
        if hand_data["index1"] == "" || hand_data["index2"] == "" || hand_data["index3"] == ""
            @warn "Hand $i has empty index fields, skipping"
            continue
        end

        push!(hands, MainData(
            hand_data["main_number"],
            get(hand_data, "manche_pb_number", 1),  # Default to 1 if missing
            hand_data["index1"],
            hand_data["index2"],
            hand_data["index3"],
            hand_data["index5"]
        ))
    end

    if isempty(hands)
        throw(InvalidGameDataError(game_number, "No valid hands found in game"))
    end

    return hands
end

# ═══════════════════════════════════════════════════════════════════════════════
# VALIDATION FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    validate_game_data(hands::Vector{MainData}, config::AnalysisConfig) -> Bool

Validate that game data meets requirements for analysis.

# Arguments
- `hands::Vector{MainData}`: Game hands to validate
- `config::AnalysisConfig`: Analysis configuration

# Returns
- `Bool`: true if data is valid

# Throws
- `InvalidGameDataError`: If data doesn't meet requirements
"""
function validate_game_data(hands::Vector{MainData}, config::AnalysisConfig)
    if length(hands) < config.min_hands
        throw(InvalidGameDataError(0, "Game has $(length(hands)) hands, minimum $(config.min_hands) required"))
    end

    # Check for sequential hand numbers
    hand_numbers = [hand.main_number for hand in hands]
    if !issorted(hand_numbers)
        @warn "Hand numbers are not in sequential order"
    end

    # Check for missing INDEX5 values
    empty_index5_count = count(hand -> isempty(hand.index5), hands)
    if empty_index5_count > 0
        @warn "Found $empty_index5_count hands with empty INDEX5 values"
    end

    return true
end

# ═══════════════════════════════════════════════════════════════════════════════
# MAIN ANALYSIS FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    create_analyzer(config::AnalysisConfig{T} = AnalysisConfig()) where T -> RealValueAnalyzer{T}

Create a new RealValueAnalyzer with the specified configuration.

# Arguments
- `config::AnalysisConfig{T}`: Analysis configuration (optional)

# Returns
- `RealValueAnalyzer{T}`: Configured analyzer

# Examples
```julia
analyzer = create_analyzer()
analyzer_f32 = create_analyzer(AnalysisConfig{Float32}())
```
"""
function create_analyzer(config::AnalysisConfig{T} = AnalysisConfig()) where T
    return RealValueAnalyzer{T}(config)
end

"""
    analyze_real_values(analyzer::RealValueAnalyzer{T}, game_number::Int,
                       dataset_path::String = DEFAULT_DATASET_PATH) where T -> AnalysisResult{T}

Analyze a complete game by calculating the 8 entropy metrics for each
INDEX5 value that actually occurred, as opposed to simulating possibilities.

# Arguments
- `analyzer::RealValueAnalyzer{T}`: Configured analyzer
- `game_number::Int`: Game number to analyze
- `dataset_path::String`: Path to dataset file (optional)

# Returns
- `AnalysisResult{T}`: Complete analysis results

# Process
1. Load data from dataset.json
2. Extract the requested game
3. For each hand (starting from hand 3):
   - Calculate 8 metrics for sequence up to this hand
   - Calculate differentials from previous hand
   - Record real observed values (no simulation)
4. Return structured results

# Examples
```julia
analyzer = create_analyzer()
results = analyze_real_values(analyzer, 1)
```
"""
function analyze_real_values(
    analyzer::RealValueAnalyzer{T},
    game_number::Int,
    dataset_path::String = DEFAULT_DATASET_PATH
) where T

    # Load and extract game data
    dataset = load_dataset(dataset_path)
    hands = extract_game_data(dataset, game_number)

    # Validate game data
    validate_game_data(hands, analyzer.config)

    # Prepare for analysis
    index5_sequence = [hand.index5 for hand in hands]
    max_hands = min(analyzer.config.max_hands, length(hands))

    # Storage for results
    hand_analyses = Vector{HandAnalysis{T}}()
    sizehint!(hand_analyses, max_hands - analyzer.config.min_hands + 1)

    # Previous metrics for differential calculation
    previous_metrics = nothing

    # Analyze each hand starting from min_hands
    for i in analyzer.config.min_hands:max_hands
        current_hand = hands[i]
        sequence_to_n = index5_sequence[1:i]

        # Calculate all metrics for this hand
        current_metrics = calculer_toutes_metriques_theoriques(
            analyzer.formulas,
            sequence_to_n,
            i
        )

        # Calculate differentials if we have previous metrics
        differentials = nothing
        if previous_metrics !== nothing
            calculator = CalculateurDifferentielsPredictifs{T}()
            differentials = calculer_differentiels_predictifs(
                calculator,
                previous_metrics,
                current_metrics
            )
        end

        # Store analysis for this hand
        push!(hand_analyses, HandAnalysis{T}(
            current_hand.main_number,
            current_hand.index5,
            current_metrics,
            differentials
        ))

        # Save for next iteration
        previous_metrics = current_metrics
    end

    # Create and return results
    return AnalysisResult{T}(
        game_number,
        length(hands),
        length(hand_analyses),
        hand_analyses,
        now(),
        analyzer.config
    )
end

# ═══════════════════════════════════════════════════════════════════════════════
# EXPORT FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    export_results(results::AnalysisResult{T}, output_file::String) where T -> String

Export analysis results to a file in the specified format.

# Arguments
- `results::AnalysisResult{T}`: Analysis results to export
- `output_file::String`: Output file path

# Returns
- `String`: Path to the created file

# Examples
```julia
results = analyze_real_values(analyzer, 1)
file_path = export_results(results, "game_1_analysis.txt")
```
"""
function export_results(results::AnalysisResult{T}, output_file::String) where T
    format = results.config.export_format

    if format == :txt
        return _export_txt(results, output_file)
    elseif format == :csv
        return _export_csv(results, output_file)
    elseif format == :json
        return _export_json(results, output_file)
    else
        throw(ArgumentError("Unsupported export format: $format"))
    end
end

"""
    export_results(results::AnalysisResult{T}) where T -> String

Export analysis results with auto-generated filename.

# Arguments
- `results::AnalysisResult{T}`: Analysis results to export

# Returns
- `String`: Path to the created file
"""
function export_results(results::AnalysisResult{T}) where T
    timestamp = Dates.format(results.analysis_timestamp, "yyyymmdd_HHMMSS")
    format_ext = string(results.config.export_format)
    filename = "real_values_analysis_game_$(results.game_number)_$(timestamp).$(format_ext)"
    return export_results(results, filename)
end

"""
    _export_txt(results::AnalysisResult{T}, output_file::String) where T -> String

Export results in TXT format (internal function).
"""
function _export_txt(results::AnalysisResult{T}, output_file::String) where T
    open(output_file, "w") do io
        # Header
        write(io, "REAL VALUES ANALYSIS REPORT\n")
        write(io, "="^80, "\n")
        write(io, "Game analyzed: $(results.game_number)\n")
        write(io, "Analysis date: $(Dates.format(results.analysis_timestamp, "dd/mm/yyyy at HH:MM:SS"))\n")
        write(io, "Total hands in game: $(results.total_hands)\n")
        write(io, "Hands analyzed: $(results.analyzed_hands)\n")
        write(io, "Analysis range: Hand $(results.config.min_hands) to Hand $(results.config.min_hands + results.analyzed_hands - 1)\n")
        write(io, "Precision: $(results.config.precision)\n")
        write(io, "\n")

        write(io, "DESCRIPTION:\n")
        write(io, "This report presents the 8 entropy metrics calculated for each\n")
        write(io, "INDEX5 value actually observed in the game, along with\n")
        write(io, "differentials from the previous hand.\n")
        write(io, "\n")

        write(io, "METRICS ANALYZED:\n")
        write(io, "1. CondT    - Conditional entropy average (PRIORITY 1)\n")
        write(io, "2. DivKLT   - Kullback-Leibler divergence (PRIORITY 2)\n")
        write(io, "3. CrossT   - Cross entropy (PRIORITY 2)\n")
        write(io, "4. MetricT  - Weighted metric entropy (PRIORITY 3)\n")
        write(io, "5. TopoT    - Multi-scale topological entropy (PRIORITY 3)\n")
        write(io, "6. TauxT    - Entropy rate (PRIORITY 4)\n")
        write(io, "7. ShannonT - Theoretical Shannon entropy (PRIORITY 5)\n")
        write(io, "8. BlockT   - Cumulative block entropy (PRIORITY 5)\n")
        write(io, "\n")

        # Table header
        write(io, "="^120, "\n")
        write(io, "DETAILED RESULTS\n")
        write(io, "="^120, "\n")
        write(io, "\n")

        # Column headers
        write(io, @sprintf("%-6s %-15s %-12s %-12s %-12s %-12s %-12s %-12s %-12s %-12s\n",
            "HAND", "INDEX5", "CondT|Diff", "DivKLT|Diff", "CrossT|Diff", "MetricT|Diff",
            "TopoT|Diff", "TauxT|Diff", "ShannonT|Diff", "BlockT|Diff"))
        write(io, "-"^120, "\n")

        # Data rows
        for analysis in results.hand_analyses
            hand_num = analysis.hand_number
            index5 = analysis.index5_observed
            metrics = analysis.metrics
            diff = analysis.differentials

            # Format metrics with differentials
            if diff !== nothing
                cond_str = @sprintf("%-6.4f|%-5.4f", metrics.cond_t, diff.diff_cond_t)
                divkl_str = @sprintf("%-6.4f|%-5.4f", metrics.div_kl_t, diff.diff_div_kl_t)
                cross_str = @sprintf("%-6.4f|%-5.4f", metrics.cross_t, diff.diff_cross_t)
                metric_str = @sprintf("%-7.4f|%-4.4f", metrics.metric_t, diff.diff_metric_t)
                topo_str = @sprintf("%-6.4f|%-5.4f", metrics.topo_t, diff.diff_topo_t)
                taux_str = @sprintf("%-6.4f|%-5.4f", metrics.taux_t, diff.diff_taux_t)
                shannon_str = @sprintf("%-7.4f|%-4.4f", metrics.shannon_t, diff.diff_shannon_t)
                block_str = @sprintf("%-7.4f|%-4.4f", metrics.block_t, diff.diff_block_t)
            else
                # First hand (no differential)
                cond_str = @sprintf("%-6.4f|%-5s", metrics.cond_t, "N/A")
                divkl_str = @sprintf("%-6.4f|%-5s", metrics.div_kl_t, "N/A")
                cross_str = @sprintf("%-6.4f|%-5s", metrics.cross_t, "N/A")
                metric_str = @sprintf("%-7.4f|%-4s", metrics.metric_t, "N/A")
                topo_str = @sprintf("%-6.4f|%-5s", metrics.topo_t, "N/A")
                taux_str = @sprintf("%-6.4f|%-5s", metrics.taux_t, "N/A")
                shannon_str = @sprintf("%-7.4f|%-4s", metrics.shannon_t, "N/A")
                block_str = @sprintf("%-7.4f|%-4s", metrics.block_t, "N/A")
            end

            write(io, @sprintf("%-6d %-15s %-12s %-12s %-12s %-12s %-12s %-12s %-12s %-12s\n",
                hand_num, index5, cond_str, divkl_str, cross_str, metric_str,
                topo_str, taux_str, shannon_str, block_str))
        end

        # Footer
        write(io, "\n")
        write(io, "="^120, "\n")
        write(io, "NOTES:\n")
        write(io, "- Values are metrics calculated for sequence [hand 1:hand n]\n")
        write(io, "- Differentials (Diff) show evolution from previous hand\n")
        write(io, "- N/A indicates no differential available (first analyzed hand)\n")
        write(io, "- All metrics use INDEX5 theoretical probabilities\n")
        write(io, "\n")
        write(io, "Generated by AnalyseurVraisValeurs.jl - $(Dates.format(now(), "dd/mm/yyyy HH:MM:SS"))\n")
    end

    return output_file
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTION MAIN POUR EXÉCUTION DIRECTE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    main_analyseur()

Fonction principale pour exécution directe du module.
Demande à l'utilisateur quelle partie analyser et lance l'analyse.
"""
function main_analyseur()
    println("🎯 ANALYSEUR DES VRAIES VALEURS OBSERVÉES")
    println("="^50)

    try
        print("Entrez le numéro de la partie à analyser : ")
        numero_partie = parse(Int, readline())

        fichier_resultat = analyser_partie_vraies_valeurs(numero_partie)

        println("\n🎉 Analyse terminée avec succès !")
        println("📁 Fichier généré : $fichier_resultat")

    catch e
        if isa(e, ArgumentError)
            println("❌ Erreur : $(e.msg)")
        else
            println("💥 Erreur inattendue : $e")
        end
    end
end

# Exécution automatique si le script est lancé directement
if abspath(PROGRAM_FILE) == @__FILE__
    main_analyseur()
end

end # module AnalyseurVraisValeurs
