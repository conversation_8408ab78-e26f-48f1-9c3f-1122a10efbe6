"""
    AnalyseurVraisValeurs

A professional Julia module for analyzing real observed INDEX5 values in baccarat games.

This module calculates the 8 entropy metrics for each INDEX5 value that actually occurred
in a game, as opposed to the prediction system which simulates 6 possibilities.

# Main Types
- [`RealValueAnalyzer`](@ref): Main analyzer for real values
- [`AnalysisResult`](@ref): Container for analysis results
- [`AnalysisConfig`](@ref): Configuration for analysis parameters

# Main Functions
- [`analyze_real_values`](@ref): Analyze a complete game with real values
- [`load_game_data`](@ref): Load data from dataset.json
- [`export_results`](@ref): Export results to TXT format

# Examples
```julia
using AnalyseurVraisValeurs

# Analyze game number 1
analyzer = RealValueAnalyzer()
results = analyze_real_values(analyzer, 1)
export_results(results, "analysis_game_1.txt")
```

# Dependencies
- JSON3: For loading dataset.json files with proper type handling
- PredicteurIndex5: For entropy calculations and data loading (Analyseur.jl)

# Compatibility
This module is fully compatible with Analyseur.jl and uses its data loading
functions (charger_donnees_partie) and calculation functions
(calculer_toutes_metriques_theoriques) to ensure consistency.

See the documentation for detailed usage examples and API reference.
"""
module AnalyseurVraisValeurs

# ═══════════════════════════════════════════════════════════════════════════════
# IMPORTS AND DEPENDENCIES
# ═══════════════════════════════════════════════════════════════════════════════

# Standard library
using Dates
using Printf

# External dependencies
using JSON3

# Internal dependencies - Import PredicteurIndex5 module
include("Analyseur.jl")
using .PredicteurIndex5

# ═══════════════════════════════════════════════════════════════════════════════
# EXPORTS
# ═══════════════════════════════════════════════════════════════════════════════

# Main types
export RealValueAnalyzer, AnalysisResult, AnalysisConfig

# Main functions
export analyze_real_values, load_game_data, export_results

# Utility functions
export create_analyzer, validate_game_data, analyze_and_export

# ═══════════════════════════════════════════════════════════════════════════════
# CONSTANTS
# ═══════════════════════════════════════════════════════════════════════════════

const DEFAULT_DATASET_PATH = "C:\\Users\\<USER>\\Desktop\\24\\modules\\partie\\dataset.json"
const MIN_HANDS_FOR_ANALYSIS = 3
const MAX_HANDS_PER_GAME = 59

# ═══════════════════════════════════════════════════════════════════════════════
# CUSTOM EXCEPTION TYPES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    DatasetError <: Exception

Exception thrown when there are issues with dataset loading or parsing.
"""
struct DatasetError <: Exception
    message::String
end

"""
    GameNotFoundError <: Exception

Exception thrown when a requested game number is not found in the dataset.
"""
struct GameNotFoundError <: Exception
    game_number::Int
    message::String
end

GameNotFoundError(game_number::Int) = GameNotFoundError(game_number, "Game number $game_number not found in dataset")

"""
    InvalidGameDataError <: Exception

Exception thrown when game data is invalid or corrupted.
"""
struct InvalidGameDataError <: Exception
    game_number::Int
    message::String
end

# ═══════════════════════════════════════════════════════════════════════════════
# TYPE DEFINITIONS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    AnalysisConfig

Simple configuration parameters for real value analysis.

# Fields
- `max_hands::Int`: Maximum number of hands to analyze (default: 59)
- `min_hands::Int`: Minimum number of hands required (default: 3)

# Examples
```julia
config = AnalysisConfig()
config_custom = AnalysisConfig(max_hands=30)
```
"""
struct AnalysisConfig
    max_hands::Int
    min_hands::Int

    function AnalysisConfig(
        max_hands::Int = MAX_HANDS_PER_GAME,
        min_hands::Int = MIN_HANDS_FOR_ANALYSIS
    )
        if max_hands < min_hands
            throw(ArgumentError("max_hands ($max_hands) must be >= min_hands ($min_hands)"))
        end
        if min_hands < 1
            throw(ArgumentError("min_hands must be >= 1"))
        end
        new(max_hands, min_hands)
    end
end

"""
    RealValueAnalyzer

Main analyzer for processing real observed INDEX5 values.

# Fields
- `config::AnalysisConfig`: Analysis configuration
- `formulas::FormulasTheoretical{Float64}`: Theoretical formulas for entropy calculations

# Examples
```julia
analyzer = RealValueAnalyzer()
analyzer_custom = RealValueAnalyzer(AnalysisConfig(max_hands=30))
```
"""
struct RealValueAnalyzer
    config::AnalysisConfig
    formulas::FormulasTheoretical{Float64}

    function RealValueAnalyzer(config::AnalysisConfig = AnalysisConfig())
        formulas = FormulasTheoretical{Float64}()
        new(config, formulas)
    end
end

"""
    HandAnalysis

Analysis results for a single hand.

# Fields
- `hand_number::Int`: Hand number in the game
- `index5_observed::String`: The actual INDEX5 value observed
- `metrics::MetriquesTheorique{Float64}`: Calculated entropy metrics
- `differentials::Union{DifferentielsPredictifs{Float64}, Nothing}`: Differentials from previous hand
"""
struct HandAnalysis
    hand_number::Int
    index5_observed::String
    metrics::MetriquesTheorique{Float64}
    differentials::Union{DifferentielsPredictifs{Float64}, Nothing}
end

"""
    AnalysisResult

Complete analysis results for a game.

# Fields
- `game_number::Int`: Game number analyzed
- `total_hands::Int`: Total number of hands in the game
- `analyzed_hands::Int`: Number of hands actually analyzed
- `hand_analyses::Vector{HandAnalysis}`: Analysis for each hand
- `analysis_timestamp::DateTime`: When the analysis was performed
- `config::AnalysisConfig`: Configuration used for analysis

# Examples
```julia
results = analyze_real_values(analyzer, 1)
println("Analyzed $(results.analyzed_hands) hands from game $(results.game_number)")
```
"""
struct AnalysisResult
    game_number::Int
    total_hands::Int
    analyzed_hands::Int
    hand_analyses::Vector{HandAnalysis}
    analysis_timestamp::DateTime
    config::AnalysisConfig
end

# ═══════════════════════════════════════════════════════════════════════════════
# DATA LOADING FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    load_game_data(game_number::Int, dataset_path::String = DEFAULT_DATASET_PATH) -> Vector{MainData}

Load game data using JSON3 to parse the dataset file with proper type handling.

# Arguments
- `game_number::Int`: Game number to load
- `dataset_path::String`: Path to the dataset file (default: DEFAULT_DATASET_PATH)

# Returns
- `Vector{MainData}`: Vector of hands in MainData format

# Throws
- `GameNotFoundError`: If the game number is not found
- `DatasetError`: If there are issues with the dataset

# Examples
```julia
hands = load_game_data(1)
hands = load_game_data(1, "custom/path/dataset.json")
```
"""
function load_game_data(game_number::Int, dataset_path::String = DEFAULT_DATASET_PATH)
    try
        # Read and parse JSON with JSON3
        if !isfile(dataset_path)
            throw(DatasetError("Dataset file not found: $dataset_path"))
        end

        json_content = read(dataset_path, String)
        data = JSON3.read(json_content)

        # Validate structure
        if !haskey(data, :parties_condensees)
            throw(DatasetError("Invalid dataset format: missing 'parties_condensees' key"))
        end

        # Find the requested game
        game_found = nothing
        for game in data.parties_condensees
            if game.partie_number == game_number
                game_found = game
                break
            end
        end

        if game_found === nothing
            throw(GameNotFoundError(game_number))
        end

        # Convert to MainData format
        hands = Vector{MainData}()
        for hand_data in game_found.mains_condensees
            # Skip invalid hands
            if hand_data.main_number === nothing ||
               hand_data.index1 === nothing ||
               hand_data.index2 === nothing ||
               hand_data.index3 === nothing ||
               hand_data.index5 === nothing
                continue
            end

            push!(hands, MainData(
                hand_data.main_number,
                hand_data.manche_pb_number,
                string(hand_data.index1),  # Convert Int to String
                string(hand_data.index2),  # Ensure String
                string(hand_data.index3),  # Ensure String
                string(hand_data.index5)   # Ensure String
            ))
        end

        if isempty(hands)
            throw(GameNotFoundError(game_number, "No valid hands found in game $game_number"))
        end

        return hands

    catch e
        if isa(e, Union{GameNotFoundError, DatasetError})
            rethrow(e)
        else
            throw(DatasetError("Failed to load game data: $e"))
        end
    end
end



# ═══════════════════════════════════════════════════════════════════════════════
# VALIDATION FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    validate_game_data(hands::Vector{MainData}, config::AnalysisConfig) -> Bool

Validate that game data meets requirements for analysis.

# Arguments
- `hands::Vector{MainData}`: Game hands to validate
- `config::AnalysisConfig`: Analysis configuration

# Returns
- `Bool`: true if data is valid

# Throws
- `InvalidGameDataError`: If data doesn't meet requirements
"""
function validate_game_data(hands::Vector{MainData}, config::AnalysisConfig)
    if length(hands) < config.min_hands
        throw(InvalidGameDataError(0, "Game has $(length(hands)) hands, minimum $(config.min_hands) required"))
    end

    # Check for sequential hand numbers
    hand_numbers = [hand.main_number for hand in hands]
    if !issorted(hand_numbers)
        @warn "Hand numbers are not in sequential order"
    end

    # Check for missing INDEX5 values
    empty_index5_count = count(hand -> isempty(hand.index5), hands)
    if empty_index5_count > 0
        @warn "Found $empty_index5_count hands with empty INDEX5 values"
    end

    return true
end

# ═══════════════════════════════════════════════════════════════════════════════
# MAIN ANALYSIS FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    create_analyzer(config::AnalysisConfig = AnalysisConfig()) -> RealValueAnalyzer

Create a new RealValueAnalyzer with the specified configuration.

# Arguments
- `config::AnalysisConfig`: Analysis configuration (optional)

# Returns
- `RealValueAnalyzer`: Configured analyzer

# Examples
```julia
analyzer = create_analyzer()
analyzer_custom = create_analyzer(AnalysisConfig(max_hands=30))
```
"""
function create_analyzer(config::AnalysisConfig = AnalysisConfig())
    return RealValueAnalyzer(config)
end

"""
    analyze_real_values(analyzer::RealValueAnalyzer, game_number::Int,
                       dataset_path::String = DEFAULT_DATASET_PATH) -> AnalysisResult

Analyze a complete game by calculating the 8 entropy metrics for each
INDEX5 value that actually occurred, as opposed to simulating possibilities.

# Arguments
- `analyzer::RealValueAnalyzer`: Configured analyzer
- `game_number::Int`: Game number to analyze
- `dataset_path::String`: Path to dataset file (optional)

# Returns
- `AnalysisResult`: Complete analysis results

# Process
1. Load data from dataset.json
2. Extract the requested game
3. For each hand (starting from hand 3):
   - Calculate 8 metrics for sequence up to this hand
   - Calculate differentials from previous hand
   - Record real observed values (no simulation)
4. Return structured results

# Examples
```julia
analyzer = create_analyzer()
results = analyze_real_values(analyzer, 1)
```
"""
function analyze_real_values(
    analyzer::RealValueAnalyzer,
    game_number::Int,
    dataset_path::String = DEFAULT_DATASET_PATH
)

    # Load game data using Analyseur.jl's function
    hands = load_game_data(game_number, dataset_path)

    # Validate game data
    validate_game_data(hands, analyzer.config)

    # Prepare for analysis
    index5_sequence = [hand.index5 for hand in hands]
    max_hands = min(analyzer.config.max_hands, length(hands))

    # Storage for results
    hand_analyses = Vector{HandAnalysis}()
    sizehint!(hand_analyses, max_hands - analyzer.config.min_hands + 1)

    # Previous metrics for differential calculation
    previous_metrics = nothing

    # Analyze each hand starting from min_hands
    for i in analyzer.config.min_hands:max_hands
        current_hand = hands[i]
        sequence_to_n = index5_sequence[1:i]

        # Calculate all metrics for this hand
        current_metrics = calculer_toutes_metriques_theoriques(
            analyzer.formulas,
            sequence_to_n,
            i
        )

        # Calculate differentials if we have previous metrics
        differentials = nothing
        if previous_metrics !== nothing
            # Calculate differentials between consecutive hands: |metric(n) - metric(n-1)|
            # This is different from prediction which calculates |metric(n+1) - metric(n)|
            differentials = DifferentielsPredictifs{Float64}(
                abs(current_metrics.cond_t - previous_metrics.cond_t),
                abs(current_metrics.divkl_t - previous_metrics.divkl_t),
                abs(current_metrics.cross_t - previous_metrics.cross_t),
                abs(current_metrics.metric_t - previous_metrics.metric_t),
                abs(current_metrics.topo_t - previous_metrics.topo_t),
                abs(current_metrics.taux_t - previous_metrics.taux_t),
                abs(current_metrics.shannon_t - previous_metrics.shannon_t),
                abs(current_metrics.block_t - previous_metrics.block_t)
            )
        end

        # Store analysis for this hand
        push!(hand_analyses, HandAnalysis(
            current_hand.main_number,
            current_hand.index5,
            current_metrics,
            differentials
        ))

        # Save for next iteration
        previous_metrics = current_metrics
    end

    # Create and return results
    return AnalysisResult(
        game_number,
        length(hands),
        length(hand_analyses),
        hand_analyses,
        now(),
        analyzer.config
    )
end

# ═══════════════════════════════════════════════════════════════════════════════
# EXPORT FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    export_results(results::AnalysisResult, output_file::String) -> String

Export analysis results to a TXT file.

# Arguments
- `results::AnalysisResult`: Analysis results to export
- `output_file::String`: Output file path

# Returns
- `String`: Path to the created file

# Examples
```julia
results = analyze_real_values(analyzer, 1)
file_path = export_results(results, "game_1_analysis.txt")
```
"""
function export_results(results::AnalysisResult, output_file::String)
    open(output_file, "w") do io
        # Header
        write(io, "REAL VALUES ANALYSIS REPORT\n")
        write(io, "="^80, "\n")
        write(io, "Game analyzed: $(results.game_number)\n")
        write(io, "Analysis date: $(Dates.format(results.analysis_timestamp, "dd/mm/yyyy at HH:MM:SS"))\n")
        write(io, "Total hands in game: $(results.total_hands)\n")
        write(io, "Hands analyzed: $(results.analyzed_hands)\n")
        write(io, "Analysis range: Hand $(results.config.min_hands) to Hand $(results.config.min_hands + results.analyzed_hands - 1)\n")
        write(io, "\n")

        write(io, "DESCRIPTION:\n")
        write(io, "This report presents the 8 entropy metrics calculated for each\n")
        write(io, "INDEX5 value actually observed in the game, along with\n")
        write(io, "differentials from the previous hand.\n")
        write(io, "\n")

        write(io, "METRICS ANALYZED:\n")
        write(io, "1. CondT    - Conditional entropy average (PRIORITY 1)\n")
        write(io, "2. DivKLT   - Kullback-Leibler divergence (PRIORITY 2)\n")
        write(io, "3. CrossT   - Cross entropy (PRIORITY 2)\n")
        write(io, "4. MetricT  - Weighted metric entropy (PRIORITY 3)\n")
        write(io, "5. TopoT    - Multi-scale topological entropy (PRIORITY 3)\n")
        write(io, "6. TauxT    - Entropy rate (PRIORITY 4)\n")
        write(io, "7. ShannonT - Theoretical Shannon entropy (PRIORITY 5)\n")
        write(io, "8. BlockT   - Cumulative block entropy (PRIORITY 5)\n")
        write(io, "\n")

        # Table header
        write(io, "="^120, "\n")
        write(io, "DETAILED RESULTS\n")
        write(io, "="^120, "\n")
        write(io, "\n")

        # Column headers
        write(io, @sprintf("%-6s %-15s %-12s %-12s %-12s %-12s %-12s %-12s %-12s %-12s\n",
            "HAND", "INDEX5", "CondT|Diff", "DivKLT|Diff", "CrossT|Diff", "MetricT|Diff",
            "TopoT|Diff", "TauxT|Diff", "ShannonT|Diff", "BlockT|Diff"))
        write(io, "-"^120, "\n")

        # Data rows
        for analysis in results.hand_analyses
            hand_num = analysis.hand_number
            index5 = analysis.index5_observed
            metrics = analysis.metrics
            diff = analysis.differentials

            # Format metrics with differentials
            if diff !== nothing
                cond_str = @sprintf("%-6.4f|%-5.4f", metrics.cond_t, diff.diff_cond_t)
                divkl_str = @sprintf("%-6.4f|%-5.4f", metrics.div_kl_t, diff.diff_div_kl_t)
                cross_str = @sprintf("%-6.4f|%-5.4f", metrics.cross_t, diff.diff_cross_t)
                metric_str = @sprintf("%-7.4f|%-4.4f", metrics.metric_t, diff.diff_metric_t)
                topo_str = @sprintf("%-6.4f|%-5.4f", metrics.topo_t, diff.diff_topo_t)
                taux_str = @sprintf("%-6.4f|%-5.4f", metrics.taux_t, diff.diff_taux_t)
                shannon_str = @sprintf("%-7.4f|%-4.4f", metrics.shannon_t, diff.diff_shannon_t)
                block_str = @sprintf("%-7.4f|%-4.4f", metrics.block_t, diff.diff_block_t)
            else
                # First hand (no differential)
                cond_str = @sprintf("%-6.4f|%-5s", metrics.cond_t, "N/A")
                divkl_str = @sprintf("%-6.4f|%-5s", metrics.div_kl_t, "N/A")
                cross_str = @sprintf("%-6.4f|%-5s", metrics.cross_t, "N/A")
                metric_str = @sprintf("%-7.4f|%-4s", metrics.metric_t, "N/A")
                topo_str = @sprintf("%-6.4f|%-5s", metrics.topo_t, "N/A")
                taux_str = @sprintf("%-6.4f|%-5s", metrics.taux_t, "N/A")
                shannon_str = @sprintf("%-7.4f|%-4s", metrics.shannon_t, "N/A")
                block_str = @sprintf("%-7.4f|%-4s", metrics.block_t, "N/A")
            end

            write(io, @sprintf("%-6d %-15s %-12s %-12s %-12s %-12s %-12s %-12s %-12s %-12s\n",
                hand_num, index5, cond_str, divkl_str, cross_str, metric_str,
                topo_str, taux_str, shannon_str, block_str))
        end

        # Footer
        write(io, "\n")
        write(io, "="^120, "\n")
        write(io, "NOTES:\n")
        write(io, "- Values are metrics calculated for sequence [hand 1:hand n]\n")
        write(io, "- Differentials (Diff) show evolution from previous hand\n")
        write(io, "- N/A indicates no differential available (first analyzed hand)\n")
        write(io, "- All metrics use INDEX5 theoretical probabilities\n")
        write(io, "\n")
        write(io, "Generated by AnalyseurVraisValeurs.jl - $(Dates.format(now(), "dd/mm/yyyy HH:MM:SS"))\n")
    end

    return output_file
end

"""
    export_results(results::AnalysisResult) -> String

Export analysis results with auto-generated filename.

# Arguments
- `results::AnalysisResult`: Analysis results to export

# Returns
- `String`: Path to the created file
"""
function export_results(results::AnalysisResult)
    timestamp = Dates.format(results.analysis_timestamp, "yyyymmdd_HHMMSS")
    filename = "real_values_analysis_game_$(results.game_number)_$(timestamp).txt"
    return export_results(results, filename)
end

# ═══════════════════════════════════════════════════════════════════════════════
# CONVENIENCE FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    analyze_and_export(game_number::Int, config::AnalysisConfig = AnalysisConfig(),
                      dataset_path::String = DEFAULT_DATASET_PATH) -> String

Convenience function to analyze a game and export results in one call.

# Arguments
- `game_number::Int`: Game number to analyze
- `config::AnalysisConfig`: Analysis configuration (optional)
- `dataset_path::String`: Path to dataset file (optional)

# Returns
- `String`: Path to the exported file

# Examples
```julia
# Simple analysis with defaults
file_path = analyze_and_export(1)

# Custom configuration
config = AnalysisConfig(max_hands=30)
file_path = analyze_and_export(1, config)

# Custom dataset path
file_path = analyze_and_export(1, AnalysisConfig(), "custom/dataset.json")
```
"""
function analyze_and_export(
    game_number::Int,
    config::AnalysisConfig = AnalysisConfig(),
    dataset_path::String = DEFAULT_DATASET_PATH
)
    analyzer = create_analyzer(config)
    results = analyze_real_values(analyzer, game_number, dataset_path)
    return export_results(results)
end

# ═══════════════════════════════════════════════════════════════════════════════
# MAIN FUNCTION FOR DIRECT EXECUTION
# ═══════════════════════════════════════════════════════════════════════════════

"""
    main()

Main function for direct script execution.
Provides interactive interface for game analysis.
"""
function main()
    println("🎯 REAL VALUES ANALYZER")
    println("="^50)

    try
        print("Enter game number to analyze: ")
        game_number = parse(Int, readline())

        print("Enter max hands to analyze (default 59): ")
        max_hands_input = strip(readline())
        max_hands = isempty(max_hands_input) ? 59 : parse(Int, max_hands_input)

        config = AnalysisConfig(max_hands=max_hands)
        file_path = analyze_and_export(game_number, config, DEFAULT_DATASET_PATH)

        println("\n✅ Analysis completed successfully!")
        println("📁 Results exported to: $file_path")

    catch e
        if isa(e, Union{GameNotFoundError, InvalidGameDataError, DatasetError})
            println("❌ Error: $(e.message)")
        elseif isa(e, ArgumentError)
            println("❌ Invalid input: $(e.msg)")
        else
            println("💥 Unexpected error: $e")
            rethrow(e)
        end
    end
end

# Auto-execute main if script is run directly
if abspath(PROGRAM_FILE) == @__FILE__
    main()
end

end # module AnalyseurVraisValeurs
