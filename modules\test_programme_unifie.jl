#!/usr/bin/env julia

"""
TEST DU PROGRAMME UNIFIÉ - PredicteurIndex5
==========================================

Script de test pour vérifier que le programme unifié fonctionne correctement
avec tous les modules de métriques intégrés.

Usage:
    julia test_programme_unifie.jl
"""

println("🧪 DÉBUT DES TESTS DU PROGRAMME UNIFIÉ")
println("=" ^ 50)

# Test 1: Chargement du programme principal
println("\n📦 Test 1: Chargement du programme principal...")
try
    # Inclure le programme principal (qui inclut tous les modules)
    include("Analyseur.txt")
    println("✅ Programme principal chargé avec succès")
catch e
    println("❌ Erreur lors du chargement : $e")
    exit(1)
end

# Test 2: Vérification des types principaux
println("\n🏗️  Test 2: Vérification des types principaux...")
try
    # Test de création des structures principales
    formulas = PredicteurIndex5.FormulasTheoretical{Float64}()
    println("✅ FormulasTheoretical créé avec succès")
    
    # Test des métriques
    metriques = PredicteurIndex5.MetriquesTheorique{Float64}(1.0, 0.8, 1.2, 0.9, 1.1, 0.7, 1.3, 1.0)
    println("✅ MetriquesTheorique créé avec succès")
    
    # Test des différentiels
    diff = PredicteurIndex5.DifferentielsPredictifs{Float64}(0.1, 0.05, 0.08, 0.03, 0.06, 0.02, 0.04, 0.07)
    println("✅ DifferentielsPredictifs créé avec succès")
    
    println("✅ Tous les types principaux fonctionnent")
catch e
    println("❌ Erreur avec les types : $e")
end

# Test 3: Test des modules de métriques individuels
println("\n🔧 Test 3: Test des modules de métriques...")
try
    # Données de test
    formulas = PredicteurIndex5.FormulasTheoretical{Float64}()
    sequence_test = ["0_A_BANKER", "1_B_PLAYER", "0_C_BANKER"]
    n_test = 3
    
    # Test de chaque module
    modules_tests = [
        ("CondT", () -> CondT.calculer_formule5B_conditionnelle_theo(formulas, sequence_test, n_test)),
        ("DivKLT", () -> DivKLT.calculer_formule6B_divergence_kl_theo(formulas, sequence_test, n_test)),
        ("CrossT", () -> CrossT.calculer_formule8B_entropie_croisee_theo(formulas, sequence_test, n_test)),
        ("MetricT", () -> MetricT.calculer_formule4B_entropie_metrique_theo(formulas, sequence_test, n_test)),
        ("TopoT", () -> TopoT.calculer_formule9B_entropie_topologique_theo(formulas, sequence_test, n_test)),
        ("TauxT", () -> TauxT.calculer_formule3B_taux_entropie_theo(formulas, sequence_test, n_test)),
        ("ShannonT", () -> ShannonT.calculer_formule1B_shannon_jointe_theo(formulas, sequence_test, n_test)),
        ("BlockT", () -> BlockT.calculer_formule10B_block_cumulative_theo(formulas, sequence_test, n_test))
    ]
    
    for (nom_module, test_func) in modules_tests
        try
            resultat = test_func()
            println("✅ $nom_module: $(round(resultat, digits=6))")
        catch e
            println("❌ $nom_module: Erreur - $e")
        end
    end
    
    println("✅ Tests des modules individuels terminés")
catch e
    println("❌ Erreur lors des tests des modules : $e")
end

# Test 4: Test de l'orchestrateur principal
println("\n🎯 Test 4: Test de l'orchestrateur principal...")
try
    # Test de la fonction principale d'orchestration
    formulas = PredicteurIndex5.FormulasTheoretical{Float64}()
    sequence_test = ["0_A_BANKER", "1_B_PLAYER", "0_C_BANKER"]
    n_test = 3
    
    # Test de calculer_toutes_metriques_theoriques
    metriques = PredicteurIndex5.calculer_toutes_metriques_theoriques(formulas, sequence_test, n_test)
    
    println("✅ Orchestrateur principal fonctionne")
    println("   📊 Métriques calculées :")
    println("      • CondT: $(round(metriques.cond_t, digits=6))")
    println("      • DivKLT: $(round(metriques.divkl_t, digits=6))")
    println("      • CrossT: $(round(metriques.cross_t, digits=6))")
    println("      • MetricT: $(round(metriques.metric_t, digits=6))")
    println("      • TopoT: $(round(metriques.topo_t, digits=6))")
    println("      • TauxT: $(round(metriques.taux_t, digits=6))")
    println("      • ShannonT: $(round(metriques.shannon_t, digits=6))")
    println("      • BlockT: $(round(metriques.block_t, digits=6))")
    
catch e
    println("❌ Erreur avec l'orchestrateur : $e")
end

# Test 5: Test des utilitaires
println("\n🛠️  Test 5: Test des utilitaires...")
try
    # Test des fonctions utilitaires
    index1_suivant = PredicteurIndex5.calculer_index1_suivant(0, "C")
    println("✅ calculer_index1_suivant: $index1_suivant")
    
    valeurs_possibles = PredicteurIndex5.generer_valeurs_possibles(1)
    println("✅ generer_valeurs_possibles: $(length(valeurs_possibles)) valeurs")
    
    println("✅ Utilitaires fonctionnent correctement")
catch e
    println("❌ Erreur avec les utilitaires : $e")
end

println("\n" * "=" ^ 50)
println("🎉 TESTS DU PROGRAMME UNIFIÉ TERMINÉS")
println("📋 Le programme est prêt à être utilisé !")
