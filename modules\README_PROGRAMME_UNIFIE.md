# 🎯 PROGRAMME UNIFIÉ - PredicteurIndex5

## 📋 Description

Programme complet d'analyse prédictive INDEX5 utilisant 8 métriques d'entropie théorique pour prédire les résultats du baccarat. Le programme central **Analyseur.txt** orchestre tous les modules de métriques pour former un système unifié.

## 🏗️ Architecture

### Programme Central
- **Analyseur.txt** (1,482 lignes) - Orchestrateur principal et interface utilisateur

### Modules de Métriques (8 modules)

#### Modules Autonomes (Niveau 1)
- **ShannonT.txt** (132 lignes) - Entropie de Shannon (PRIORITÉ 5)
- **TopoT.txt** (179 lignes) - Entropie Topologique (PRIORITÉ 3)
- **DivKLT.txt** (154 lignes) - Divergence Kullback-Leibler (PRIORITÉ 2)
- **CrossT.txt** (154 lignes) - Entropie Croisée (PRIORITÉ 2)

#### Modules avec Dépendances (Niveau 2)
- **CondT.txt** (315 lignes) - Entropie Conditionnelle (PRIORITÉ 1)
- **MetricT.txt** (212 lignes) - Entropie Métrique (PRIORITÉ 3)
- **TauxT.txt** (315 lignes) - Taux d'Entropie (PRIORITÉ 4)
- **BlockT.txt** (237 lignes) - Entropie de Bloc (PRIORITÉ 5)

## 🚀 Utilisation

### Lancement du Programme
```julia
# Méthode 1: Exécution directe
julia Analyseur.txt

# Méthode 2: Import en tant que module
include("Analyseur.txt")
using .PredicteurIndex5
```

### Test du Programme
```bash
# Exécuter les tests de validation
julia test_programme_unifie.jl
```

### Utilisation Interactive
```julia
# Lancer le programme principal
PredicteurIndex5.main()

# Ou utiliser les fonctions individuellement
formulas = PredicteurIndex5.FormulasTheoretical{Float64}()
sequence = ["0_A_BANKER", "1_B_PLAYER", "0_C_BANKER"]
metriques = PredicteurIndex5.calculer_toutes_metriques_theoriques(formulas, sequence, 3)
```

## 📊 Fonctionnalités

### Calcul des Métriques
- ✅ 8 métriques d'entropie théorique
- ✅ Calcul coordonné par ordre de priorité
- ✅ Différentiels prédictifs
- ✅ Optimisation des performances

### Prédiction
- ✅ Prédiction INDEX5 basée sur les métriques
- ✅ Génération des 6 valeurs possibles
- ✅ Calcul des règles de transition INDEX1
- ✅ Prédicteur avancé avec statistiques

### Interface
- ✅ Chargement automatique des données JSON
- ✅ Interface utilisateur interactive
- ✅ Affichage formaté des résultats
- ✅ Export des prédictions

### Utilitaires
- ✅ Gestion des fichiers JSON
- ✅ Validation des données
- ✅ Gestion d'erreurs robuste
- ✅ Documentation complète

## 🔧 Structure des Données

### Types Principaux
```julia
# Structure des données d'une main
MainData(main_number, manche_pb_number, index1, index2, index3, index5)

# Formules théoriques avec 18 probabilités INDEX5
FormulasTheoretical{Float64}(base, epsilon, theoretical_probs, sequence_complete)

# 8 métriques théoriques
MetriquesTheorique{Float64}(cond_t, divkl_t, cross_t, metric_t, topo_t, taux_t, shannon_t, block_t)

# Différentiels prédictifs
DifferentielsPredictifs{Float64}(diff_cond_t, diff_divkl_t, ...)

# Résultat de prédiction complet
PredictionResult(main_actuelle, index5_actuel, index1_suivant, ...)
```

## 📈 Ordre de Priorité Prédictive

1. **CondT** - Entropie Conditionnelle (prévisibilité immédiate)
2. **DivKLT** - Divergence KL (biais du modèle)
3. **CrossT** - Entropie Croisée (inefficacité du modèle)
4. **MetricT** - Entropie Métrique (variation de complexité)
5. **TopoT** - Entropie Topologique (patterns multi-échelles)
6. **TauxT** - Taux d'Entropie (complexité normalisée)
7. **ShannonT** - Entropie de Shannon (diversité observée)
8. **BlockT** - Entropie Jointe (complexité totale)

## 🎯 Flux d'Exécution

```
main() → Chargement données → Boucle prédiction
    ↓
calculer_toutes_metriques_theoriques()
    ↓
[CondT, DivKLT, CrossT, MetricT, TopoT, TauxT, ShannonT, BlockT]
    ↓
calculer_differentiels_predictifs()
    ↓
predire_main_suivante()
    ↓
afficher_prediction()
```

## 🔍 Validation

Le programme inclut :
- ✅ Tests automatisés (`test_programme_unifie.jl`)
- ✅ Validation des paramètres d'entrée
- ✅ Gestion d'erreurs robuste
- ✅ Vérification de l'intégration des modules
- ✅ Messages d'état détaillés

## 📝 Notes Techniques

- **Langage** : Julia (optimisé pour les calculs numériques)
- **Types** : Paramétriques pour flexibilité (Float32, Float64, etc.)
- **Performance** : Pré-allocation des vecteurs, optimisations mémoire
- **Robustesse** : Validation systématique, gestion d'exceptions
- **Maintenabilité** : Architecture modulaire, documentation complète

## 🎲 Application

Ce programme unifié forme un **système complet d'analyse prédictive INDEX5** pour le baccarat, utilisant des métriques d'entropie théorique avancées pour maximiser la précision des prédictions.
