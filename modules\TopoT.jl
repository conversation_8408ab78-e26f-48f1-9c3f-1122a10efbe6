"""
MODULE JULIA AUTONOME - TopoT
=============================

MÉTRIQUE AUTONOME - TopoT (PRIORITÉ 3)
FORMULE 9B : Entropie Topologique Multi-Échelles (VERSION THÉORIQUE CORRIGÉE)
Entropie topologique multi-échelles basée sur la complexité théorique INDEX5 à 3 niveaux de résolution.

FORMULE : TopoT_n = 0.167 × H_theo(blocs_1) + 0.333 × H_theo(blocs_2) + 0.500 × H_theo(blocs_3)
Pondération basée sur la capacité informationnelle théorique de chaque échelle.

USAGE :
    using TopoT
    formulas = FormulasTheoretical{Float64}()
    sequence = ["0_A_BANKER", "1_B_PLAYER", "0_C_TIE", "1_A_BANKER"]
    result = calculer_formule9B_entropie_topologique_theo(formulas, sequence, 4)

DÉPENDANCES : AUCUNE - Module complètement autonome
"""

module TopoT

# ═══════════════════════════════════════════════════════════════════════════════
# STRUCTURE CENTRALISÉE
# ═══════════════════════════════════════════════════════════════════════════════

# Import de la structure FormulasTheoretical depuis le module parent
import ..FormulasTheoretical

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTION PRINCIPALE - TopoT
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_formule9B_entropie_topologique_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

Calcule l'entropie topologique multi-échelles basée sur la complexité théorique INDEX5 à 3 niveaux de résolution.

FORMULE : TopoT_n = 0.167 × H_theo(blocs_1) + 0.333 × H_theo(blocs_2) + 0.500 × H_theo(blocs_3)

ÉCHELLES D'ANALYSE :
- Échelle 1 (16.7%) : Complexité locale - valeurs individuelles
- Échelle 2 (33.3%) : Complexité des transitions - paires consécutives
- Échelle 3 (50.0%) : Complexité des motifs - triplets consécutifs

CALCUL PAR ÉCHELLE :
Pour chaque échelle k, H_theo(blocs_k) = -∑ p_theo(bloc) × log₂(p_theo(bloc))
Où p_theo(bloc) = ∏ p_theo(valeur) pour chaque valeur dans le bloc (hypothèse d'indépendance)

PONDÉRATION THÉORIQUE :
- Les motifs longs (triplets) ont plus d'importance (50%) car ils capturent plus de structure
- Les transitions (paires) ont une importance moyenne (33.3%)
- Les valeurs individuelles ont moins d'importance (16.7%)

INTERPRÉTATION :
- TopoT élevé : Structure complexe avec beaucoup de motifs différents
- TopoT faible : Structure simple avec peu de motifs distincts
- Sensible aux patterns multi-échelles dans la séquence

PARAMÈTRES :
- formulas : Structure contenant les probabilités théoriques INDEX5
- sequence : Séquence de valeurs INDEX5 (ex: ["0_A_BANKER", "1_B_PLAYER", ...])
- n : Longueur de la sous-séquence à analyser [1:n]

RETOUR :
- Entropie topologique pondérée en bits (si base=2.0)
"""
function calculer_formule9B_entropie_topologique_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Fonction auxiliaire pour calculer H_theo(blocs_k) avec probabilités théoriques INDEX5
    function calculer_entropie_blocs(block_size::Int)
        if block_size > length(subsequence)
            return zero(T)
        end

        # Extraire tous les blocs de taille block_size
        blocs_distincts = Set{Vector{String}}()
        for i in 1:(length(subsequence) - block_size + 1)
            bloc = subsequence[i:i+block_size-1]
            push!(blocs_distincts, bloc)
        end

        # Calculer l'entropie théorique des blocs distincts
        entropy = zero(T)
        for bloc in blocs_distincts
            # Calculer la probabilité théorique du bloc sous hypothèse d'indépendance
            p_bloc_theo = one(T)
            for value in bloc
                p_value_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
                p_bloc_theo *= p_value_theo
            end

            if p_bloc_theo > zero(T)
                # Entropie de Shannon pour ce bloc : -p_theo(bloc) × log₂(p_theo(bloc))
                entropy -= p_bloc_theo * (log(p_bloc_theo) / log(formulas.base))
            end
        end

        return entropy
    end

    # Calculer les entropies pour chaque échelle
    h_blocs_1 = calculer_entropie_blocs(1)  # Valeurs individuelles (échelle locale)
    h_blocs_2 = n >= 2 ? calculer_entropie_blocs(2) : zero(T)  # Paires consécutives (échelle transitions)
    h_blocs_3 = n >= 3 ? calculer_entropie_blocs(3) : zero(T)  # Triplets consécutifs (échelle motifs)

    # Poids théoriques basés sur la capacité informationnelle de chaque échelle
    w1 = T(0.167)  # 16.7% pour complexité locale (valeurs individuelles)
    w2 = T(0.333)  # 33.3% pour complexité des transitions (paires)
    w3 = T(0.500)  # 50.0% pour complexité des motifs (triplets)

    # Entropie topologique multi-échelles pondérée
    topo_entropy = w1 * h_blocs_1 + w2 * h_blocs_2 + w3 * h_blocs_3

    return topo_entropy
end

# ═══════════════════════════════════════════════════════════════════════════════
# EXPORTS
# ═══════════════════════════════════════════════════════════════════════════════

export calculer_formule9B_entropie_topologique_theo

end # module TopoT
