#!/usr/bin/env julia

"""
TEST D'INTÉGRATION - Programme Unifié PredicteurIndex5
=====================================================

Script de test pour vérifier que tous les modules .jl fonctionnent ensemble.

Usage:
    cd modules
    julia test_integration.jl
"""

println("🧪 TEST D'INTÉGRATION DU PROGRAMME UNIFIÉ")
println("=" ^ 60)

# Test 1: Vérification de la présence des fichiers
println("\n📁 Test 1: Vérification des fichiers modules...")
modules_requis = [
    "Analyseur.jl",
    "CondT.jl", "DivKLT.jl", "CrossT.jl", "MetricT.jl",
    "TopoT.jl", "TauxT.jl", "ShannonT.jl", "BlockT.jl"
]

for module_file in modules_requis
    if isfile(module_file)
        println("✅ $module_file trouvé")
    else
        println("❌ $module_file MANQUANT")
        exit(1)
    end
end

# Test 2: Chargement des modules individuels
println("\n🔧 Test 2: Chargement des modules individuels...")
modules_status = []

# Test des modules autonomes d'abord
modules_autonomes = ["ShannonT", "TopoT", "DivKLT", "CrossT"]
for module_name in modules_autonomes
    try
        include("$(module_name).jl")
        println("✅ $module_name chargé avec succès")
        push!(modules_status, (module_name, true))
    catch e
        println("❌ $module_name: Erreur - $e")
        push!(modules_status, (module_name, false))
    end
end

# Test des modules avec dépendances
modules_dependants = ["CondT", "MetricT", "TauxT", "BlockT"]
for module_name in modules_dependants
    try
        include("$(module_name).jl")
        println("✅ $module_name chargé avec succès")
        push!(modules_status, (module_name, true))
    catch e
        println("❌ $module_name: Erreur - $e")
        push!(modules_status, (module_name, false))
    end
end

# Test 3: Chargement du programme principal
println("\n🎯 Test 3: Chargement du programme principal...")
try
    include("Analyseur.jl")
    println("✅ Analyseur.jl (programme principal) chargé avec succès")
    
    # Vérifier que le module PredicteurIndex5 est disponible
    if isdefined(Main, :PredicteurIndex5)
        println("✅ Module PredicteurIndex5 disponible")
    else
        println("❌ Module PredicteurIndex5 non disponible")
    end
    
catch e
    println("❌ Erreur lors du chargement du programme principal : $e")
    exit(1)
end

# Test 4: Test des fonctions principales
println("\n⚙️  Test 4: Test des fonctions principales...")
try
    # Test de création des structures
    formulas = PredicteurIndex5.FormulasTheoretical{Float64}()
    println("✅ FormulasTheoretical créé")
    
    # Test avec une séquence simple
    sequence_test = ["0_A_BANKER", "1_B_PLAYER", "0_C_BANKER"]
    n_test = 3
    
    # Test de l'orchestrateur principal
    metriques = PredicteurIndex5.calculer_toutes_metriques_theoriques(formulas, sequence_test, n_test)
    println("✅ calculer_toutes_metriques_theoriques fonctionne")
    
    # Afficher les résultats
    println("   📊 Résultats des métriques :")
    println("      • CondT: $(round(metriques.cond_t, digits=4))")
    println("      • DivKLT: $(round(metriques.divkl_t, digits=4))")
    println("      • CrossT: $(round(metriques.cross_t, digits=4))")
    println("      • MetricT: $(round(metriques.metric_t, digits=4))")
    println("      • TopoT: $(round(metriques.topo_t, digits=4))")
    println("      • TauxT: $(round(metriques.taux_t, digits=4))")
    println("      • ShannonT: $(round(metriques.shannon_t, digits=4))")
    println("      • BlockT: $(round(metriques.block_t, digits=4))")
    
    # Test des utilitaires
    index1_suivant = PredicteurIndex5.calculer_index1_suivant(0, "C")
    println("✅ calculer_index1_suivant: $index1_suivant")
    
    valeurs_possibles = PredicteurIndex5.generer_valeurs_possibles(1)
    println("✅ generer_valeurs_possibles: $(length(valeurs_possibles)) valeurs")
    
catch e
    println("❌ Erreur lors des tests fonctionnels : $e")
end

# Test 5: Résumé final
println("\n📋 Test 5: Résumé de l'intégration...")
modules_ok = sum(status for (_, status) in modules_status)
total_modules = length(modules_status)

println("📊 Statut des modules :")
for (nom, status) in modules_status
    status_icon = status ? "✅" : "❌"
    println("   $status_icon $nom")
end

println("\n🎯 RÉSULTAT FINAL :")
if modules_ok == total_modules
    println("🎉 SUCCÈS COMPLET ! Tous les modules sont intégrés et fonctionnels")
    println("🚀 Le programme unifié est prêt à être utilisé")
    println("\n💡 Pour lancer le programme principal :")
    println("   julia Analyseur.jl")
else
    println("⚠️  INTÉGRATION PARTIELLE : $modules_ok/$total_modules modules OK")
    println("🔧 Vérifiez les modules en erreur avant utilisation")
end

println("\n" * "=" ^ 60)
println("🏁 TEST D'INTÉGRATION TERMINÉ")
