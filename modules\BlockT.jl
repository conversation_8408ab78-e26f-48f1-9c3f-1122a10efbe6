"""
MODULE JULIA AUTONOME - BlockT
==============================

MÉTRIQUE AVEC DÉPENDANCES - BlockT (PRIORITÉ 5)
FORMULE 10B : Entropie de Bloc Vraie (VERSION CORRIGÉE SELON DOCUMENTATION)
Calcule la vraie entropie jointe selon la règle de chaîne généralisée.

FORMULE : BlockT_n = H(X₁,...,Xₙ) = ∑ᵢ₌₁ⁿ H(Xᵢ|X₁,...,Xᵢ₋₁)
Basée sur la règle de chaîne : p(x₁,...,xₙ) = p(x₁) × ∏ᵢ₌₂ⁿ p(xᵢ|x₁,...,xᵢ₋₁)

USAGE :
    using BlockT
    formulas = FormulasTheoretical{Float64}()
    sequence = ["0_A_BANKER", "1_B_PLAYER", "0_C_TIE"]
    result = calculer_formule10B_block_cumulative_theo(formulas, sequence, 3)

DÉPENDANCES INCLUSES : Module Support Probabilités complet (3 fonctions) - Module complètement autonome
"""

module BlockT

# ═══════════════════════════════════════════════════════════════════════════════
# STRUCTURES DE DONNÉES
# ═══════════════════════════════════════════════════════════════════════════════

"""
Structure contenant les formules théoriques INDEX5 pour les calculs d'entropie.
"""
struct FormulasTheoretical{T<:AbstractFloat}
    base::T                                    # Base logarithmique (2.0 pour bits)
    epsilon::T                                 # Valeur epsilon pour éviter log(0)
    theoretical_probs::Dict{String,T}          # 18 probabilités théoriques INDEX5
    sequence_complete::Vector{String}          # Séquence complète (UTILISÉE pour BlockT)
end

# ═══════════════════════════════════════════════════════════════════════════════
# CONSTRUCTEUR AVEC PROBABILITÉS INDEX5 EXACTES
# ═══════════════════════════════════════════════════════════════════════════════

"""
Constructeur par défaut avec les 18 probabilités théoriques INDEX5 exactes.
"""
function FormulasTheoretical{T}(
    base::T = T(2.0),
    epsilon::T = T(1e-12),
    sequence_complete::Vector{String} = String[]
) where T<:AbstractFloat

    # Les 18 probabilités théoriques INDEX5 exactes (depuis partie1.txt lignes 59-69)
    theoretical_probs = Dict{String,T}(
        "0_A_BANKER" => T(0.085136),
        "1_A_BANKER" => T(0.086389),
        "0_B_BANKER" => T(0.064676),
        "1_B_BANKER" => T(0.065479),
        "0_C_BANKER" => T(0.077903),
        "1_C_BANKER" => T(0.078929),
        "0_A_PLAYER" => T(0.085240),
        "1_A_PLAYER" => T(0.086361),
        "0_B_PLAYER" => T(0.076907),
        "1_B_PLAYER" => T(0.077888),
        "0_C_PLAYER" => T(0.059617),
        "1_C_PLAYER" => T(0.060352),
        "0_A_TIE" => T(0.017719),
        "1_A_TIE" => T(0.017978),
        "0_B_TIE" => T(0.016281),
        "1_B_TIE" => T(0.016482),
        "0_C_TIE" => T(0.013241),
        "1_C_TIE" => T(0.013423)
    )

    return FormulasTheoretical{T}(base, epsilon, theoretical_probs, sequence_complete)
end

# ═══════════════════════════════════════════════════════════════════════════════
# MODULE SUPPORT PROBABILITÉS (INCLUS POUR AUTONOMIE)
# ═══════════════════════════════════════════════════════════════════════════════

"""
SUPPORT PROBABILITÉS - Niveau 1 (Base)
Calcule la probabilité conditionnelle empirique p_empirique(xᵢ|xᵢ₋₁) basée sur les observations
dans la séquence complète disponible dans formulas.
"""
function calculer_probabilite_conditionnelle_empirique(
    formulas::FormulasTheoretical{T},
    current_value::String,
    previous_value::String
) where T<:AbstractFloat

    # Accéder à la séquence complète stockée dans formulas
    if isempty(formulas.sequence_complete)
        # Fallback : utiliser la probabilité théorique marginale
        return get(formulas.theoretical_probs, current_value, formulas.epsilon)
    end

    sequence_complete = formulas.sequence_complete

    # Compter les transitions previous_value → current_value
    count_transition = 0
    count_previous = 0

    for i in 1:(length(sequence_complete) - 1)
        if sequence_complete[i] == previous_value
            count_previous += 1
            if sequence_complete[i + 1] == current_value
                count_transition += 1
            end
        end
    end

    # Calculer p_empirique(current|previous) = count(previous → current) / count(previous)
    if count_previous > 0
        return T(count_transition) / T(count_previous)
    else
        # Si previous_value n'a jamais été observé, utiliser la probabilité théorique marginale
        return get(formulas.theoretical_probs, current_value, formulas.epsilon)
    end
end

"""
SUPPORT PROBABILITÉS - Niveau 2
Calcule la probabilité jointe théorique d'une séquence en utilisant les probabilités conditionnelles
empiriques basées sur les observations de la séquence complète.
"""
function calculer_probabilite_jointe_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String}
) where T<:AbstractFloat

    if isempty(sequence)
        return zero(T)
    end

    if length(sequence) == 1
        # p_theo(x₁) - utiliser la probabilité théorique
        return get(formulas.theoretical_probs, sequence[1], formulas.epsilon)
    end

    # Pour les séquences plus longues, utiliser les probabilités conditionnelles empiriques
    joint_prob = one(T)

    # Premier terme : p_theo(x₁) - probabilité théorique du premier élément
    first_value = sequence[1]
    p_first = get(formulas.theoretical_probs, first_value, formulas.epsilon)
    joint_prob *= p_first

    # Termes suivants : p_empirique(xᵢ|xᵢ₋₁) basé sur les observations
    for i in 2:length(sequence)
        current_value = sequence[i]
        previous_value = sequence[i-1]

        # Calculer p_empirique(xᵢ|xᵢ₋₁) basé sur les observations dans la séquence complète
        p_conditional_empirique = calculer_probabilite_conditionnelle_empirique(
            formulas, current_value, previous_value
        )

        joint_prob *= p_conditional_empirique
    end

    return joint_prob
end

"""
SUPPORT PROBABILITÉS - Niveau 3
Calcule la vraie probabilité conditionnelle théorique p_theo(xᵢ|x₁,...,xᵢ₋₁)
Sans hypothèse d'indépendance - utilise les probabilités jointes théoriques.
"""
function calculer_probabilite_conditionnelle_theo(
    formulas::FormulasTheoretical{T},
    current_value::String,
    context::Vector{String}
) where T<:AbstractFloat

    # Construire la séquence complète : context + current_value
    full_sequence = vcat(context, [current_value])

    # Calculer p_theo(x₁,...,xᵢ) - probabilité jointe de la séquence complète
    p_joint_full = calculer_probabilite_jointe_theo(formulas, full_sequence)

    # Calculer p_theo(x₁,...,xᵢ₋₁) - probabilité jointe du contexte
    p_joint_context = calculer_probabilite_jointe_theo(formulas, context)

    # p_theo(xᵢ|x₁,...,xᵢ₋₁) = p_theo(x₁,...,xᵢ) / p_theo(x₁,...,xᵢ₋₁)
    if p_joint_context > zero(T)
        return p_joint_full / p_joint_context
    else
        # Si le contexte a une probabilité nulle, utiliser la probabilité marginale
        return get(formulas.theoretical_probs, current_value, formulas.epsilon)
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTION PRINCIPALE - BlockT
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_formule10B_block_cumulative_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

Calcule la vraie entropie jointe selon la règle de chaîne généralisée.
Représente l'entropie totale de la séquence [1:n] en tenant compte de toutes les dépendances.

FORMULE : BlockT_n = H(X₁,...,Xₙ) = ∑ᵢ₌₁ⁿ H(Xᵢ|X₁,...,Xᵢ₋₁)

RÈGLE DE CHAÎNE : p(x₁,...,xₙ) = p(x₁) × ∏ᵢ₌₂ⁿ p(xᵢ|x₁,...,xᵢ₋₁)
Donc : H(X₁,...,Xₙ) = -log₂(p(x₁,...,xₙ)) = ∑ᵢ₌₁ⁿ [-log₂(p(xᵢ|x₁,...,xᵢ₋₁))]

CALCUL DÉTAILLÉ :
- H(X₁) = -log₂(p_theo(x₁)) (probabilité théorique INDEX5)
- H(Xᵢ|X₁,...,Xᵢ₋₁) = -log₂(p_theo(xᵢ|x₁,...,xᵢ₋₁)) pour i > 1

PROBABILITÉS CONDITIONNELLES :
- Utilise le Module Support Probabilités complet
- Combine probabilités théoriques INDEX5 et observations empiriques
- Tient compte des vraies dépendances dans la séquence

INTERPRÉTATION :
- BlockT élevé : Séquence très imprévisible, beaucoup d'information
- BlockT faible : Séquence prévisible, peu d'information nouvelle
- BlockT = ∑ᵢ₌₁ⁿ CondT_i : Somme des entropies conditionnelles

RELATION AVEC AUTRES MÉTRIQUES :
- TauxT_n = BlockT_n / n (taux d'entropie)
- CondT_n = BlockT_n / n (entropie conditionnelle moyenne)

PARAMÈTRES :
- formulas : Structure contenant les probabilités théoriques INDEX5 et séquence complète
- sequence : Séquence de valeurs INDEX5 (ex: ["0_A_BANKER", "1_B_PLAYER", ...])
- n : Longueur de la sous-séquence à analyser [1:n]

RETOUR :
- Entropie jointe totale en bits (si base=2.0)
"""
function calculer_formule10B_block_cumulative_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Calculer l'entropie jointe selon la règle de chaîne généralisée :
    # H(X₁,...,Xₙ) = ∑ᵢ₌₁ⁿ H(Xᵢ|X₁,...,Xᵢ₋₁)

    total_joint_entropy = zero(T)

    for i in 1:n
        if i == 1
            # H(X₁) = -log₂(p_theo(x₁))
            first_value = sequence[1]
            p_first = get(formulas.theoretical_probs, first_value, formulas.epsilon)
            if p_first > zero(T)
                total_joint_entropy += -(log(p_first) / log(formulas.base))
            end
        else
            # H(Xᵢ|X₁,...,Xᵢ₋₁) = -log₂(p_theo(xᵢ|x₁,...,xᵢ₋₁))
            current_value = sequence[i]
            context = sequence[1:i-1]

            # Calculer la vraie probabilité conditionnelle p_theo(xᵢ|x₁,...,xᵢ₋₁)
            # Utilise les fonctions du Module Support Probabilités incluses
            p_conditional = calculer_probabilite_conditionnelle_theo(
                formulas, current_value, context
            )

            if p_conditional > zero(T)
                total_joint_entropy += -(log(p_conditional) / log(formulas.base))
            end
        end
    end

    return total_joint_entropy
end

# ═══════════════════════════════════════════════════════════════════════════════
# EXPORTS
# ═══════════════════════════════════════════════════════════════════════════════

export FormulasTheoretical, calculer_formule10B_block_cumulative_theo

end # module BlockT
