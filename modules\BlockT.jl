"""
MODULE JULIA AUTONOME - BlockT
==============================

MÉTRIQUE AVEC DÉPENDANCES - BlockT (PRIORITÉ 5)
FORMULE 10B : Entropie de Bloc Vraie (VERSION CORRIGÉE SELON DOCUMENTATION)
Calcule la vraie entropie jointe selon la règle de chaîne généralisée.

FORMULE : BlockT_n = H(X₁,...,Xₙ) = ∑ᵢ₌₁ⁿ H(Xᵢ|X₁,...,Xᵢ₋₁)
Basée sur la règle de chaîne : p(x₁,...,xₙ) = p(x₁) × ∏ᵢ₌₂ⁿ p(xᵢ|x₁,...,xᵢ₋₁)

USAGE :
    using BlockT
    formulas = FormulasTheoretical{Float64}()
    sequence = ["0_A_BANKER", "1_B_PLAYER", "0_C_TIE"]
    result = calculer_formule10B_block_cumulative_theo(formulas, sequence, 3)

DÉPENDANCES INCLUSES : Module Support Probabilités complet (3 fonctions) - Module complètement autonome
"""

module BlockT

# ═══════════════════════════════════════════════════════════════════════════════
# STRUCTURE CENTRALISÉE
# ═══════════════════════════════════════════════════════════════════════════════

# Import de la structure FormulasTheoretical depuis le module parent
import ..FormulasTheoretical

# ═══════════════════════════════════════════════════════════════════════════════
# MODULE SUPPORT PROBABILITÉS (INCLUS POUR AUTONOMIE)
# ═══════════════════════════════════════════════════════════════════════════════

"""
SUPPORT PROBABILITÉS - Niveau 1 (Base)
Calcule la probabilité conditionnelle empirique p_empirique(xᵢ|xᵢ₋₁) basée sur les observations
dans la séquence complète disponible dans formulas.
"""
function calculer_probabilite_conditionnelle_empirique(
    formulas::FormulasTheoretical{T},
    current_value::String,
    previous_value::String
) where T<:AbstractFloat

    # Accéder à la séquence complète stockée dans formulas
    if isempty(formulas.sequence_complete)
        # Fallback : utiliser la probabilité théorique marginale
        return get(formulas.theoretical_probs, current_value, formulas.epsilon)
    end

    sequence_complete = formulas.sequence_complete

    # Compter les transitions previous_value → current_value
    count_transition = 0
    count_previous = 0

    for i in 1:(length(sequence_complete) - 1)
        if sequence_complete[i] == previous_value
            count_previous += 1
            if sequence_complete[i + 1] == current_value
                count_transition += 1
            end
        end
    end

    # Calculer p_empirique(current|previous) = count(previous → current) / count(previous)
    if count_previous > 0
        return T(count_transition) / T(count_previous)
    else
        # Si previous_value n'a jamais été observé, utiliser la probabilité théorique marginale
        return get(formulas.theoretical_probs, current_value, formulas.epsilon)
    end
end

"""
SUPPORT PROBABILITÉS - Niveau 2
Calcule la probabilité jointe théorique d'une séquence en utilisant les probabilités conditionnelles
empiriques basées sur les observations de la séquence complète.
"""
function calculer_probabilite_jointe_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String}
) where T<:AbstractFloat

    if isempty(sequence)
        return zero(T)
    end

    if length(sequence) == 1
        # p_theo(x₁) - utiliser la probabilité théorique
        return get(formulas.theoretical_probs, sequence[1], formulas.epsilon)
    end

    # Pour les séquences plus longues, utiliser les probabilités conditionnelles empiriques
    joint_prob = one(T)

    # Premier terme : p_theo(x₁) - probabilité théorique du premier élément
    first_value = sequence[1]
    p_first = get(formulas.theoretical_probs, first_value, formulas.epsilon)
    joint_prob *= p_first

    # Termes suivants : p_empirique(xᵢ|xᵢ₋₁) basé sur les observations
    for i in 2:length(sequence)
        current_value = sequence[i]
        previous_value = sequence[i-1]

        # Calculer p_empirique(xᵢ|xᵢ₋₁) basé sur les observations dans la séquence complète
        p_conditional_empirique = calculer_probabilite_conditionnelle_empirique(
            formulas, current_value, previous_value
        )

        joint_prob *= p_conditional_empirique
    end

    return joint_prob
end

"""
SUPPORT PROBABILITÉS - Niveau 3
Calcule la vraie probabilité conditionnelle théorique p_theo(xᵢ|x₁,...,xᵢ₋₁)
Sans hypothèse d'indépendance - utilise les probabilités jointes théoriques.
"""
function calculer_probabilite_conditionnelle_theo(
    formulas::FormulasTheoretical{T},
    current_value::String,
    context::Vector{String}
) where T<:AbstractFloat

    # Construire la séquence complète : context + current_value
    full_sequence = vcat(context, [current_value])

    # Calculer p_theo(x₁,...,xᵢ) - probabilité jointe de la séquence complète
    p_joint_full = calculer_probabilite_jointe_theo(formulas, full_sequence)

    # Calculer p_theo(x₁,...,xᵢ₋₁) - probabilité jointe du contexte
    p_joint_context = calculer_probabilite_jointe_theo(formulas, context)

    # p_theo(xᵢ|x₁,...,xᵢ₋₁) = p_theo(x₁,...,xᵢ) / p_theo(x₁,...,xᵢ₋₁)
    if p_joint_context > zero(T)
        return p_joint_full / p_joint_context
    else
        # Si le contexte a une probabilité nulle, utiliser la probabilité marginale
        return get(formulas.theoretical_probs, current_value, formulas.epsilon)
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTION PRINCIPALE - BlockT
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_formule10B_block_cumulative_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

Calcule l'entropie de bloc cumulative selon la formule du cours d'entropie adaptée aux séquences observées.

FORMULE DU COURS ADAPTÉE :
H_n = -∑ p_théo(séquence_observée) log₂ p_théo(séquence_observée)

Où la somme porte sur toutes les sous-séquences de longueur croissante (1 à n) observées dans la partie.

CALCUL DÉTAILLÉ :
- Pour chaque longueur k de 1 à n
- Pour chaque sous-séquence de longueur k dans la partie observée
- Calculer p_théo(sous-séquence) avec probabilités INDEX5
- Appliquer : -p_théo(seq) × log₂(p_théo(seq))
- Sommer tous les termes

PROBABILITÉS JOINTES :
- Utilise le Module Support Probabilités complet
- Combine probabilités théoriques INDEX5 et observations empiriques
- Calcul via règle de chaîne : p(x₁,...,xₖ) = p(x₁) × ∏ᵢ₌₂ᵏ p(xᵢ|x₁,...,xᵢ₋₁)

INTERPRÉTATION :
- BlockT élevé : Séquences observées très surprenantes selon INDEX5
- BlockT faible : Séquences observées prévisibles selon INDEX5
- Mesure la complexité informationnelle cumulative de la partie

RELATION AVEC AUTRES MÉTRIQUES :
- TauxT_n = BlockT_n / n (taux d'entropie)
- Base pour calculs d'entropie normalisés

PARAMÈTRES :
- formulas : Structure contenant les probabilités théoriques INDEX5 et séquence complète
- sequence : Séquence de valeurs INDEX5 (ex: ["0_A_BANKER", "1_B_PLAYER", ...])
- n : Longueur maximale des sous-séquences à analyser

RETOUR :
- Entropie de bloc cumulative en bits (si base=2.0)
"""
function calculer_formule10B_block_cumulative_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Calculer l'entropie de bloc de longueur n selon la formule du cours :
    # H_n = -∑ p_théo(séquence_observée) log₂ p_théo(séquence_observée)
    # Où ∑ porte sur toutes les sous-séquences de longueur n observées

    block_entropy = zero(T)

    # Parcourir toutes les sous-séquences de longueur n observées
    for start_pos in 1:(length(sequence) - n + 1)
        # Extraire la sous-séquence de longueur n
        subseq = sequence[start_pos:start_pos + n - 1]

        # Calculer p_théo(sous-séquence) via probabilités jointes
        p_joint = calculer_probabilite_jointe_theo(formulas, subseq)

        # Appliquer la formule du cours : -p_théo(seq) × log₂(p_théo(seq))
        if p_joint > zero(T)
            block_entropy += -p_joint * (log(p_joint) / log(formulas.base))
        end
    end

    return block_entropy
end

# ═══════════════════════════════════════════════════════════════════════════════
# EXPORTS
# ═══════════════════════════════════════════════════════════════════════════════

export calculer_formule10B_block_cumulative_theo

end # module BlockT
