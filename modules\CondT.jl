"""
MODULE JULIA AUTONOME - CondT
=============================

MÉTRIQUE AVEC DÉPENDANCES - CondT (PRIORITÉ 1)
FORMULE 5B : Entropie Conditionnelle Cumulative (VERSION THÉORIQUE CORRIGÉE)
Entropie conditionnelle cumulative moyenne de toute la séquence [1:n] selon la Chain Rule.

FORMULE : CondT_n = (1/n) × ∑ᵢ₌₁ⁿ H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
Relation : CondT_n = H_theo(X₁,...,Xₙ) / n
Signification : Prévisibilité globale du système INDEX5

USAGE :
    using CondT
    formulas = FormulasTheoretical{Float64}()
    sequence = ["0_A_BANKER", "1_B_PLAYER", "0_C_TIE"]
    result = calculer_formule5B_conditionnelle_theo(formulas, sequence, 3)

DÉPENDANCES INCLUSES : calculer_formule1B_shannon_jointe_theo (ShannonT) - Module complètement autonome
"""

module CondT

# ═══════════════════════════════════════════════════════════════════════════════
# STRUCTURES DE DONNÉES
# ═══════════════════════════════════════════════════════════════════════════════

"""
Structure contenant les formules théoriques INDEX5 pour les calculs d'entropie.
"""
struct FormulasTheoretical{T<:AbstractFloat}
    base::T                                    # Base logarithmique (2.0 pour bits)
    epsilon::T                                 # Valeur epsilon pour éviter log(0)
    theoretical_probs::Dict{String,T}          # 18 probabilités théoriques INDEX5
    sequence_complete::Vector{String}          # Séquence complète (non utilisée pour CondT)
end

# ═══════════════════════════════════════════════════════════════════════════════
# CONSTRUCTEUR AVEC PROBABILITÉS INDEX5 EXACTES
# ═══════════════════════════════════════════════════════════════════════════════

"""
Constructeur par défaut avec les 18 probabilités théoriques INDEX5 exactes.
"""
function FormulasTheoretical{T}(
    base::T = T(2.0),
    epsilon::T = T(1e-12)
) where T<:AbstractFloat

    # Les 18 probabilités théoriques INDEX5 exactes (depuis partie1.txt lignes 59-69)
    theoretical_probs = Dict{String,T}(
        "0_A_BANKER" => T(0.085136),
        "1_A_BANKER" => T(0.086389),
        "0_B_BANKER" => T(0.064676),
        "1_B_BANKER" => T(0.065479),
        "0_C_BANKER" => T(0.077903),
        "1_C_BANKER" => T(0.078929),
        "0_A_PLAYER" => T(0.085240),
        "1_A_PLAYER" => T(0.086361),
        "0_B_PLAYER" => T(0.076907),
        "1_B_PLAYER" => T(0.077888),
        "0_C_PLAYER" => T(0.059617),
        "1_C_PLAYER" => T(0.060352),
        "0_A_TIE" => T(0.017719),
        "1_A_TIE" => T(0.017978),
        "0_B_TIE" => T(0.016281),
        "1_B_TIE" => T(0.016482),
        "0_C_TIE" => T(0.013241),
        "1_C_TIE" => T(0.013423)
    )

    return FormulasTheoretical{T}(base, epsilon, theoretical_probs, String[])
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTION DÉPENDANCE - ShannonT (INCLUSE POUR AUTONOMIE)
# ═══════════════════════════════════════════════════════════════════════════════

"""
Fonction ShannonT incluse pour rendre le module CondT autonome.
Calcule l'entropie de Shannon théorique pour une séquence croissante [main 1 : main n].
"""
function calculer_formule1B_shannon_jointe_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences observées dans la séquence
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer l'entropie de Shannon théorique basée sur les probabilités INDEX5
    entropy = zero(T)

    # Pour chaque valeur unique observée, utiliser sa probabilité théorique INDEX5
    for (value, count) in counts
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
        if p_theo > zero(T)
            # Entropie de Shannon pure : H(X) = -∑ p_theo(x) log₂(p_theo(x))
            entropy -= p_theo * (log(p_theo) / log(formulas.base))
        end
    end

    return entropy
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTION PRINCIPALE - CondT
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_formule5B_conditionnelle_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

Calcule l'entropie conditionnelle cumulative moyenne de toute la séquence [1:n] selon la Chain Rule.
Mesure la prévisibilité globale du système INDEX5.

FORMULE : CondT_n = (1/n) × ∑ᵢ₌₁ⁿ H_theo(Xᵢ | X₁,...,Xᵢ₋₁)

RELATION FONDAMENTALE : CondT_n = H_theo(X₁,...,Xₙ) / n
Cette relation découle de la règle de chaîne : H(X₁,...,Xₙ) = ∑ᵢ₌₁ⁿ H(Xᵢ | X₁,...,Xᵢ₋₁)

CALCUL DES ENTROPIES CONDITIONNELLES :
- H_theo(X₁) = -log₂(p_theo(x₁)) (pas de conditionnement)
- H_theo(Xᵢ | X₁,...,Xᵢ₋₁) = H_theo(X₁,...,Xᵢ) - H_theo(X₁,...,Xᵢ₋₁) (règle de chaîne)

INTERPRÉTATION PRÉDICTIVE :
- CondT faible : Système très prévisible, chaque nouvelle valeur est bien prédite par l'historique
- CondT élevé : Système imprévisible, chaque nouvelle valeur apporte beaucoup d'information nouvelle
- CondT = 0 : Système parfaitement déterministe (impossible en pratique)
- CondT = H_max : Système complètement aléatoire

USAGE EN ANALYSE PRÉDICTIVE :
- PRIORITÉ 1 : Métrique la plus importante pour la prédiction
- Mesure directe de la prévisibilité du système INDEX5
- Indicateur de la qualité potentielle des prédictions
- Base pour évaluer l'efficacité des modèles prédictifs

PROPRIÉTÉS MATHÉMATIQUES :
- Toujours ≥ 0 (propriété de l'entropie)
- Décroît généralement avec n si le système a de la structure
- Converge vers le taux d'entropie pour les processus stationnaires

PARAMÈTRES :
- formulas : Structure contenant les probabilités théoriques INDEX5
- sequence : Séquence de valeurs INDEX5 (ex: ["0_A_BANKER", "1_B_PLAYER", ...])
- n : Longueur de la sous-séquence à analyser [1:n]

RETOUR :
- Entropie conditionnelle cumulative moyenne en bits (si base=2.0)
"""
function calculer_formule5B_conditionnelle_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Fonction auxiliaire pour calculer H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    function calculer_entropie_conditionnelle(i::Int)
        if i == 1
            # H_theo(X₁) - pas de conditionnement
            value = sequence[i]
            p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
            return p_theo > zero(T) ? -(log(p_theo) / log(formulas.base)) : zero(T)
        else
            # H_theo(Xᵢ | X₁,...,Xᵢ₋₁) = H_theo(X₁,...,Xᵢ) - H_theo(X₁,...,Xᵢ₋₁)
            # Utilise la fonction ShannonT incluse dans ce module
            h_i = calculer_formule1B_shannon_jointe_theo(formulas, sequence, i)
            h_i_minus_1 = calculer_formule1B_shannon_jointe_theo(formulas, sequence, i-1)
            return h_i - h_i_minus_1
        end
    end

    # Calculer la somme des entropies conditionnelles : ∑ᵢ₌₁ⁿ H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    somme_entropies_conditionnelles = zero(T)
    for i in 1:n
        h_cond = calculer_entropie_conditionnelle(i)
        somme_entropies_conditionnelles += h_cond
    end

    # Retourner la moyenne : CondT_n = (1/n) × ∑ᵢ₌₁ⁿ H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    # Cette moyenne représente la prévisibilité globale du système
    return somme_entropies_conditionnelles / T(n)
end

# ═══════════════════════════════════════════════════════════════════════════════
# EXPORTS
# ═══════════════════════════════════════════════════════════════════════════════

export FormulasTheoretical, calculer_formule5B_conditionnelle_theo

end # module CondT
